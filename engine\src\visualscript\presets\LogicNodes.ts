/**
 * 视觉脚本逻辑节点
 * 提供逻辑运算相关的节点
 */
import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { AsyncNode, AsyncNodeOptions } from '../nodes/AsyncNode';
import { NodeCategory, NodeType, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry, NodeTypeInfo } from '../nodes/NodeRegistry';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 比较运算符类型
 */
enum ComparisonOperator {
  EQUAL = 'equal',
  NOT_EQUAL = 'notEqual',
  GREATER = 'greater',
  GREATER_EQUAL = 'greaterEqual',
  LESS = 'less',
  LESS_EQUAL = 'lessEqual',
  CONTAINS = 'contains',
  STARTS_WITH = 'startsWith',
  ENDS_WITH = 'endsWith',
  REGEX_MATCH = 'regexMatch'
}

/**
 * 逻辑运算符类型
 */
enum LogicalOperator {
  AND = 'and',
  OR = 'or',
  NOT = 'not',
  XOR = 'xor',
  NAND = 'nand',
  NOR = 'nor'
}

/**
 * 循环类型
 */
enum LoopType {
  WHILE = 'while',
  FOR = 'for',
  FOR_EACH = 'forEach',
  DO_WHILE = 'doWhile'
}

/**
 * 状态机状态
 */
interface StateMachineState {
  name: string;
  onEnter?: () => void;
  onExit?: () => void;
  transitions: Map<string, string>;
}

/**
 * 分支节点
 * 根据条件选择执行路径
 */
export class BranchNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加条件输入
    this.addInput({
      name: 'condition',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '条件',
      defaultValue: false
    });

    // 添加真值输出流程插槽
    this.addOutput({
      name: 'true',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '条件为真时执行'
    });

    // 添加假值输出流程插槽
    this.addOutput({
      name: 'false',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '条件为假时执行'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取条件值
    const condition = this.getInputValue('condition') as boolean;

    // 根据条件选择执行路径
    if (condition) {
      this.triggerFlow('true');
      return true;
    } else {
      this.triggerFlow('false');
      return false;
    }
  }
}

/**
 * 比较节点
 * 比较两个值
 */
export class ComparisonNode extends FunctionNode {
  /** 比较运算符 */
  private operator: ComparisonOperator;

  /**
   * 创建比较节点
   * @param options 节点选项
   */
  constructor(options: any) {
    super(options);

    // 设置比较运算符
    this.operator = options.metadata?.operator || ComparisonOperator.EQUAL;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加第一个值输入
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'any',
      description: '第一个值',
      defaultValue: 0
    });

    // 添加第二个值输入
    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'any',
      description: '第二个值',
      defaultValue: 0
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '比较结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const a = this.getInputValue('a');
    const b = this.getInputValue('b');

    // 根据运算符比较值
    let result: boolean;

    switch (this.operator) {
      case ComparisonOperator.EQUAL:
        result = a === b;
        break;
      case ComparisonOperator.NOT_EQUAL:
        result = a !== b;
        break;
      case ComparisonOperator.GREATER:
        result = a > b;
        break;
      case ComparisonOperator.GREATER_EQUAL:
        result = a >= b;
        break;
      case ComparisonOperator.LESS:
        result = a < b;
        break;
      case ComparisonOperator.LESS_EQUAL:
        result = a <= b;
        break;
      case ComparisonOperator.CONTAINS:
        result = this.performContainsComparison(a, b);
        break;
      case ComparisonOperator.STARTS_WITH:
        result = this.performStartsWithComparison(a, b);
        break;
      case ComparisonOperator.ENDS_WITH:
        result = this.performEndsWithComparison(a, b);
        break;
      case ComparisonOperator.REGEX_MATCH:
        result = this.performRegexComparison(a, b);
        break;
      default:
        result = false;
    }

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }

  /**
   * 执行包含比较
   * @param a 第一个值
   * @param b 第二个值
   * @returns 比较结果
   */
  private performContainsComparison(a: any, b: any): boolean {
    if (typeof a === 'string' && typeof b === 'string') {
      return a.includes(b);
    }
    if (Array.isArray(a)) {
      return a.includes(b);
    }
    if (typeof a === 'object' && a !== null) {
      return Object.values(a).includes(b);
    }
    return false;
  }

  /**
   * 执行开始于比较
   * @param a 第一个值
   * @param b 第二个值
   * @returns 比较结果
   */
  private performStartsWithComparison(a: any, b: any): boolean {
    if (typeof a === 'string' && typeof b === 'string') {
      return a.startsWith(b);
    }
    if (Array.isArray(a) && Array.isArray(b)) {
      return b.every((item, index) => a[index] === item);
    }
    return false;
  }

  /**
   * 执行结束于比较
   * @param a 第一个值
   * @param b 第二个值
   * @returns 比较结果
   */
  private performEndsWithComparison(a: any, b: any): boolean {
    if (typeof a === 'string' && typeof b === 'string') {
      return a.endsWith(b);
    }
    if (Array.isArray(a) && Array.isArray(b)) {
      const startIndex = a.length - b.length;
      return startIndex >= 0 && b.every((item, index) => a[startIndex + index] === item);
    }
    return false;
  }

  /**
   * 执行正则表达式比较
   * @param a 第一个值（要匹配的字符串）
   * @param b 第二个值（正则表达式模式）
   * @returns 比较结果
   */
  private performRegexComparison(a: any, b: any): boolean {
    try {
      const text = String(a);
      const pattern = String(b);
      const regex = new RegExp(pattern);
      return regex.test(text);
    } catch (error) {
      console.error(`[正则比较] 正则表达式错误: ${error}`);
      return false;
    }
  }
}

/**
 * 逻辑运算节点
 * 执行逻辑运算
 */
export class LogicalOperationNode extends FunctionNode {
  /** 逻辑运算符 */
  private operator: LogicalOperator;

  /**
   * 创建逻辑运算节点
   * @param options 节点选项
   */
  constructor(options: any) {
    super(options);

    // 设置逻辑运算符
    this.operator = options.metadata?.operator || LogicalOperator.AND;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 根据运算符添加输入
    if (this.operator !== LogicalOperator.NOT) {
      // 添加第一个值输入
      this.addInput({
        name: 'a',
        type: SocketType.DATA,
        direction: SocketDirection.INPUT,
        dataType: 'boolean',
        description: '第一个值',
        defaultValue: false
      });

      // 添加第二个值输入
      this.addInput({
        name: 'b',
        type: SocketType.DATA,
        direction: SocketDirection.INPUT,
        dataType: 'boolean',
        description: '第二个值',
        defaultValue: false
      });
    } else {
      // 添加值输入
      this.addInput({
        name: 'value',
        type: SocketType.DATA,
        direction: SocketDirection.INPUT,
        dataType: 'boolean',
        description: '值',
        defaultValue: false
      });
    }

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '运算结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    let result: boolean;

    // 根据运算符执行逻辑运算
    switch (this.operator) {
      case LogicalOperator.AND:
        const a = this.getInputValue('a') as boolean;
        const b = this.getInputValue('b') as boolean;
        result = a && b;
        break;
      case LogicalOperator.OR:
        const c = this.getInputValue('a') as boolean;
        const d = this.getInputValue('b') as boolean;
        result = c || d;
        break;
      case LogicalOperator.NOT:
        const value = this.getInputValue('value') as boolean;
        result = !value;
        break;
      default:
        result = false;
    }

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 开关节点
 * 在两个状态之间切换
 */
export class ToggleNode extends FunctionNode {
  /** 当前状态 */
  private state: boolean = false;

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加重置输入
    this.addInput({
      name: 'reset',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '重置状态',
      defaultValue: false
    });

    // 添加初始状态输入
    this.addInput({
      name: 'initialState',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '初始状态',
      defaultValue: false
    });

    // 添加当前状态输出
    this.addOutput({
      name: 'state',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '当前状态'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const reset = this.getInputValue('reset') as boolean;
    const initialState = this.getInputValue('initialState') as boolean;

    // 如果需要重置，则设置为初始状态
    if (reset) {
      this.state = initialState;
    } else {
      // 否则切换状态
      this.state = !this.state;
    }

    // 设置输出值
    this.setOutputValue('state', this.state);

    // 触发输出流程
    this.triggerFlow('flow');

    return this.state;
  }
}

/**
 * 多路分支节点（Switch/Case）
 * 根据输入值选择对应的执行路径
 */
export class SwitchNode extends FlowNode {
  /** 分支选项 */
  private cases: Map<any, string> = new Map();

  constructor(options: any) {
    super(options);

    // 从元数据中获取分支配置
    const cases = options.metadata?.cases || [];
    for (const caseItem of cases) {
      this.cases.set(caseItem.value, caseItem.output);
    }
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加选择值输入
    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'any',
      description: '选择值'
    });

    // 添加默认输出流程插槽
    this.addOutput({
      name: 'default',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '默认执行路径'
    });

    // 动态添加分支输出
    for (const [value, outputName] of this.cases.entries()) {
      this.addOutput({
        name: outputName,
        type: SocketType.FLOW,
        direction: SocketDirection.OUTPUT,
        description: `值为 ${value} 时执行`
      });
    }
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取选择值
    const value = this.getInputValue('value');

    // 查找匹配的分支
    for (const [caseValue, outputName] of this.cases.entries()) {
      if (this.isValueMatch(value, caseValue)) {
        this.triggerFlow(outputName);
        return value;
      }
    }

    // 如果没有匹配的分支，执行默认路径
    this.triggerFlow('default');
    return value;
  }

  /**
   * 检查值是否匹配
   * @param value 输入值
   * @param caseValue 分支值
   * @returns 是否匹配
   */
  private isValueMatch(value: any, caseValue: any): boolean {
    // 严格相等比较
    if (value === caseValue) {
      return true;
    }

    // 如果是字符串，尝试类型转换比较
    if (typeof value === 'string' && typeof caseValue !== 'string') {
      return value === String(caseValue);
    }

    if (typeof caseValue === 'string' && typeof value !== 'string') {
      return String(value) === caseValue;
    }

    return false;
  }

  /**
   * 添加分支
   * @param value 分支值
   * @param outputName 输出名称
   */
  public addCase(value: any, outputName: string): void {
    this.cases.set(value, outputName);

    // 添加对应的输出插槽
    this.addOutput({
      name: outputName,
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: `值为 ${value} 时执行`
    });
  }

  /**
   * 移除分支
   * @param value 分支值
   */
  public removeCase(value: any): void {
    const outputName = this.cases.get(value);
    if (outputName) {
      this.cases.delete(value);
      // 注意：这里应该移除对应的输出插槽，但需要确保没有连接
    }
  }
}

/**
 * While循环节点
 * 当条件为真时重复执行
 */
export class WhileLoopNode extends FlowNode {
  /** 当前循环次数 */
  private currentIteration: number = 0;
  /** 最大循环次数限制 */
  private maxIterations: number = 1000;
  /** 是否正在执行循环 */
  private isLooping: boolean = false;

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '开始循环'
    });

    // 添加条件输入
    this.addInput({
      name: 'condition',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '循环条件',
      defaultValue: false
    });

    // 添加最大迭代次数输入
    this.addInput({
      name: 'maxIterations',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '最大迭代次数',
      defaultValue: 1000,
      optional: true
    });

    // 添加循环体输出流程插槽
    this.addOutput({
      name: 'loopBody',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '循环体执行'
    });

    // 添加完成输出流程插槽
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '循环完成'
    });

    // 添加中断输出流程插槽
    this.addOutput({
      name: 'break',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '循环中断'
    });

    // 添加当前迭代次数输出
    this.addOutput({
      name: 'iteration',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '当前迭代次数'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const condition = this.getInputValue('condition') as boolean;
    const maxIterations = this.getInputValue('maxIterations') as number || this.maxIterations;

    // 如果不在循环中，开始新的循环
    if (!this.isLooping) {
      this.currentIteration = 0;
      this.isLooping = true;
    }

    // 检查循环条件和最大迭代次数
    if (condition && this.currentIteration < maxIterations) {
      // 增加迭代次数
      this.currentIteration++;

      // 设置输出值
      this.setOutputValue('iteration', this.currentIteration);

      // 触发循环体执行
      this.triggerFlow('loopBody');

      // 注意：在实际实现中，这里需要等待循环体执行完成后再次检查条件
      // 这可能需要异步处理或者特殊的执行机制

      return this.currentIteration;
    } else {
      // 循环结束
      this.isLooping = false;

      if (this.currentIteration >= maxIterations) {
        // 达到最大迭代次数，触发中断
        console.warn(`[While循环] 达到最大迭代次数限制: ${maxIterations}`);
        this.triggerFlow('break');
      } else {
        // 正常完成
        this.triggerFlow('completed');
      }

      return this.currentIteration;
    }
  }

  /**
   * 重置循环状态
   */
  public resetLoop(): void {
    this.currentIteration = 0;
    this.isLooping = false;
  }

  /**
   * 强制中断循环
   */
  public breakLoop(): void {
    this.isLooping = false;
    this.triggerFlow('break');
  }
}

/**
 * ForEach循环节点
 * 遍历数组或对象的每个元素
 */
export class ForEachLoopNode extends FlowNode {
  /** 当前索引 */
  private currentIndex: number = 0;
  /** 当前数组或对象 */
  private currentArray: any[] = [];
  /** 是否正在执行循环 */
  private isLooping: boolean = false;

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '开始循环'
    });

    // 添加数组输入
    this.addInput({
      name: 'array',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'array',
      description: '要遍历的数组'
    });

    // 添加循环体输出流程插槽
    this.addOutput({
      name: 'loopBody',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '循环体执行'
    });

    // 添加完成输出流程插槽
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '循环完成'
    });

    // 添加当前元素输出
    this.addOutput({
      name: 'element',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'any',
      description: '当前元素'
    });

    // 添加当前索引输出
    this.addOutput({
      name: 'index',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '当前索引'
    });

    // 添加数组长度输出
    this.addOutput({
      name: 'length',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '数组长度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入数组
    const inputArray = this.getInputValue('array');

    // 如果不在循环中，开始新的循环
    if (!this.isLooping) {
      this.currentIndex = 0;
      this.currentArray = this.normalizeArray(inputArray);
      this.isLooping = true;

      // 设置数组长度输出
      this.setOutputValue('length', this.currentArray.length);
    }

    // 检查是否还有元素需要处理
    if (this.currentIndex < this.currentArray.length) {
      // 获取当前元素
      const currentElement = this.currentArray[this.currentIndex];

      // 设置输出值
      this.setOutputValue('element', currentElement);
      this.setOutputValue('index', this.currentIndex);

      // 增加索引
      this.currentIndex++;

      // 触发循环体执行
      this.triggerFlow('loopBody');

      return currentElement;
    } else {
      // 循环结束
      this.isLooping = false;
      this.triggerFlow('completed');

      return null;
    }
  }

  /**
   * 标准化数组输入
   * @param input 输入值
   * @returns 标准化后的数组
   */
  private normalizeArray(input: any): any[] {
    if (Array.isArray(input)) {
      return input;
    }

    if (typeof input === 'object' && input !== null) {
      // 如果是对象，返回其值的数组
      return Object.values(input);
    }

    if (typeof input === 'string') {
      // 如果是字符串，返回字符数组
      return input.split('');
    }

    // 其他情况返回包含单个元素的数组
    return [input];
  }

  /**
   * 重置循环状态
   */
  public resetLoop(): void {
    this.currentIndex = 0;
    this.currentArray = [];
    this.isLooping = false;
  }
}

/**
 * 状态机节点
 * 实现有限状态机逻辑
 */
export class StateMachineNode extends FlowNode {
  /** 状态定义 */
  private states: Map<string, StateMachineState> = new Map();
  /** 当前状态 */
  private currentState: string = '';
  /** 初始状态 */
  private initialState: string = '';

  constructor(options: any) {
    super(options);

    // 从元数据中获取状态配置
    const statesConfig = options.metadata?.states || [];
    for (const stateConfig of statesConfig) {
      const state: StateMachineState = {
        name: stateConfig.name,
        transitions: new Map(Object.entries(stateConfig.transitions || {}))
      };
      this.states.set(state.name, state);
    }

    this.initialState = options.metadata?.initialState || '';
    this.currentState = this.initialState;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加事件输入
    this.addInput({
      name: 'event',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '触发事件'
    });

    // 添加重置输入
    this.addInput({
      name: 'reset',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '重置到初始状态',
      defaultValue: false,
      optional: true
    });

    // 添加当前状态输出
    this.addOutput({
      name: 'currentState',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '当前状态'
    });

    // 添加状态变化输出
    this.addOutput({
      name: 'stateChanged',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '状态变化时触发'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    // 为每个状态添加输出插槽
    for (const stateName of this.states.keys()) {
      this.addOutput({
        name: `state_${stateName}`,
        type: SocketType.FLOW,
        direction: SocketDirection.OUTPUT,
        description: `进入状态 ${stateName} 时触发`
      });
    }
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const event = this.getInputValue('event') as string;
    const reset = this.getInputValue('reset') as boolean;

    // 如果需要重置
    if (reset) {
      this.currentState = this.initialState;
      this.setOutputValue('currentState', this.currentState);
      this.triggerFlow('stateChanged');
      this.triggerFlow(`state_${this.currentState}`);
      this.triggerFlow('flow');
      return this.currentState;
    }

    // 如果有事件输入，尝试状态转换
    if (event) {
      const newState = this.processTransition(event);
      if (newState && newState !== this.currentState) {
        const oldState = this.currentState;
        this.currentState = newState;

        console.log(`[状态机] 状态转换: ${oldState} -> ${newState} (事件: ${event})`);

        // 设置输出值
        this.setOutputValue('currentState', this.currentState);

        // 触发状态变化事件
        this.triggerFlow('stateChanged');
        this.triggerFlow(`state_${this.currentState}`);
      }
    }

    // 设置当前状态输出
    this.setOutputValue('currentState', this.currentState);

    // 触发输出流程
    this.triggerFlow('flow');

    return this.currentState;
  }

  /**
   * 处理状态转换
   * @param event 触发事件
   * @returns 新状态名称
   */
  private processTransition(event: string): string | null {
    const currentStateObj = this.states.get(this.currentState);
    if (!currentStateObj) {
      console.warn(`[状态机] 未找到当前状态: ${this.currentState}`);
      return null;
    }

    const newState = currentStateObj.transitions.get(event);
    if (!newState) {
      console.warn(`[状态机] 状态 ${this.currentState} 没有事件 ${event} 的转换`);
      return null;
    }

    if (!this.states.has(newState)) {
      console.warn(`[状态机] 目标状态不存在: ${newState}`);
      return null;
    }

    return newState;
  }

  /**
   * 添加状态
   * @param name 状态名称
   * @param transitions 转换映射
   */
  public addState(name: string, transitions: Record<string, string> = {}): void {
    const state: StateMachineState = {
      name,
      transitions: new Map(Object.entries(transitions))
    };
    this.states.set(name, state);

    // 添加对应的输出插槽
    this.addOutput({
      name: `state_${name}`,
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: `进入状态 ${name} 时触发`
    });
  }

  /**
   * 添加状态转换
   * @param fromState 源状态
   * @param event 触发事件
   * @param toState 目标状态
   */
  public addTransition(fromState: string, event: string, toState: string): void {
    const state = this.states.get(fromState);
    if (state) {
      state.transitions.set(event, toState);
    }
  }

  /**
   * 获取当前状态
   * @returns 当前状态名称
   */
  public getCurrentState(): string {
    return this.currentState;
  }

  /**
   * 获取所有状态
   * @returns 状态映射
   */
  public getStates(): Map<string, StateMachineState> {
    return new Map(this.states);
  }
}

/**
 * 扩展逻辑运算节点
 * 支持XOR、NAND、NOR等扩展逻辑运算
 */
export class ExtendedLogicalOperationNode extends FunctionNode {
  /** 逻辑运算符 */
  private operator: LogicalOperator;

  /**
   * 创建扩展逻辑运算节点
   * @param options 节点选项
   */
  constructor(options: any) {
    super(options);

    // 设置逻辑运算符
    this.operator = options.metadata?.operator || LogicalOperator.XOR;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 根据运算符添加输入
    if (this.operator !== LogicalOperator.NOT) {
      // 添加第一个值输入
      this.addInput({
        name: 'a',
        type: SocketType.DATA,
        direction: SocketDirection.INPUT,
        dataType: 'boolean',
        description: '第一个值',
        defaultValue: false
      });

      // 添加第二个值输入
      this.addInput({
        name: 'b',
        type: SocketType.DATA,
        direction: SocketDirection.INPUT,
        dataType: 'boolean',
        description: '第二个值',
        defaultValue: false
      });
    } else {
      // 添加值输入
      this.addInput({
        name: 'value',
        type: SocketType.DATA,
        direction: SocketDirection.INPUT,
        dataType: 'boolean',
        description: '值',
        defaultValue: false
      });
    }

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '运算结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    let result: boolean;

    // 根据运算符执行逻辑运算
    switch (this.operator) {
      case LogicalOperator.XOR:
        const a1 = this.getInputValue('a') as boolean;
        const b1 = this.getInputValue('b') as boolean;
        result = (a1 && !b1) || (!a1 && b1);
        break;
      case LogicalOperator.NAND:
        const a2 = this.getInputValue('a') as boolean;
        const b2 = this.getInputValue('b') as boolean;
        result = !(a2 && b2);
        break;
      case LogicalOperator.NOR:
        const a3 = this.getInputValue('a') as boolean;
        const b3 = this.getInputValue('b') as boolean;
        result = !(a3 || b3);
        break;
      case LogicalOperator.NOT:
        const value = this.getInputValue('value') as boolean;
        result = !value;
        break;
      default:
        result = false;
    }

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 条件表达式解析器节点
 * 解析和执行字符串形式的条件表达式
 */
export class ConditionExpressionNode extends FunctionNode {
  /** 表达式缓存 */
  private expressionCache: Map<string, Function> = new Map();

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加表达式输入
    this.addInput({
      name: 'expression',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '条件表达式',
      defaultValue: 'true'
    });

    // 添加变量上下文输入
    this.addInput({
      name: 'context',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '变量上下文',
      defaultValue: {},
      optional: true
    });

    // 添加缓存启用输入
    this.addInput({
      name: 'enableCache',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '启用表达式缓存',
      defaultValue: true,
      optional: true
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '表达式结果'
    });

    // 添加错误输出
    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '解析错误信息'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    // 添加错误流程插槽
    this.addOutput({
      name: 'errorFlow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '解析错误时触发'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const expression = this.getInputValue('expression') as string;
    const context = this.getInputValue('context') as Record<string, any> || {};
    const enableCache = this.getInputValue('enableCache') as boolean;

    try {
      // 解析和执行表达式
      const result = this.evaluateExpression(expression, context, enableCache);

      // 设置输出值
      this.setOutputValue('result', result);
      this.setOutputValue('error', '');

      // 触发输出流程
      this.triggerFlow('flow');

      return result;
    } catch (error) {
      // 处理错误
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`[条件表达式] 解析错误: ${errorMessage}`);

      // 设置错误输出
      this.setOutputValue('result', false);
      this.setOutputValue('error', errorMessage);

      // 触发错误流程
      this.triggerFlow('errorFlow');

      return false;
    }
  }

  /**
   * 解析和执行表达式
   * @param expression 表达式字符串
   * @param context 变量上下文
   * @param enableCache 是否启用缓存
   * @returns 表达式结果
   */
  private evaluateExpression(expression: string, context: Record<string, any>, enableCache: boolean): boolean {
    // 检查缓存
    if (enableCache && this.expressionCache.has(expression)) {
      const cachedFunction = this.expressionCache.get(expression)!;
      return cachedFunction(context);
    }

    // 安全的表达式解析
    const safeExpression = this.sanitizeExpression(expression);

    // 创建函数
    const functionBody = `
      with (context) {
        return ${safeExpression};
      }
    `;

    const evaluatorFunction = new Function('context', functionBody);

    // 缓存函数
    if (enableCache) {
      this.expressionCache.set(expression, evaluatorFunction);
    }

    // 执行函数
    return Boolean(evaluatorFunction(context));
  }

  /**
   * 清理和验证表达式
   * @param expression 原始表达式
   * @returns 清理后的表达式
   */
  private sanitizeExpression(expression: string): string {
    // 移除危险的关键字和函数调用
    const dangerousPatterns = [
      /\beval\b/g,
      /\bFunction\b/g,
      /\bsetTimeout\b/g,
      /\bsetInterval\b/g,
      /\brequire\b/g,
      /\bimport\b/g,
      /\bexport\b/g,
      /\bdelete\b/g,
      /\bwindow\b/g,
      /\bdocument\b/g,
      /\bglobal\b/g,
      /\bprocess\b/g
    ];

    let sanitized = expression;
    for (const pattern of dangerousPatterns) {
      if (pattern.test(sanitized)) {
        throw new Error(`表达式包含不安全的内容: ${pattern.source}`);
      }
    }

    // 替换常见的逻辑运算符
    sanitized = sanitized
      .replace(/\band\b/gi, '&&')
      .replace(/\bor\b/gi, '||')
      .replace(/\bnot\b/gi, '!')
      .replace(/\beq\b/gi, '===')
      .replace(/\bne\b/gi, '!==')
      .replace(/\bgt\b/gi, '>')
      .replace(/\bge\b/gi, '>=')
      .replace(/\blt\b/gi, '<')
      .replace(/\ble\b/gi, '<=');

    return sanitized;
  }

  /**
   * 清空表达式缓存
   */
  public clearCache(): void {
    this.expressionCache.clear();
  }

  /**
   * 获取缓存大小
   * @returns 缓存中的表达式数量
   */
  public getCacheSize(): number {
    return this.expressionCache.size;
  }
}

/**
 * 注册逻辑节点
 * @param registry 节点注册表
 */
export function registerLogicNodes(registry: NodeRegistry): void {
  // 注册分支节点
  registry.registerNodeType({
    type: 'logic/flow/branch',
    category: NodeCategory.LOGIC,
    constructor: BranchNode,
    label: '分支',
    description: '根据条件选择执行路径',
    icon: 'branch',
    color: '#FF9800',
    tags: ['logic', 'flow', 'branch']
  });

  // 注册多路分支节点
  registry.registerNodeType({
    type: 'logic/flow/switch',
    category: NodeCategory.LOGIC,
    constructor: SwitchNode,
    label: '多路分支',
    description: '根据输入值选择对应的执行路径',
    icon: 'switch',
    color: '#FF9800',
    tags: ['logic', 'flow', 'switch', 'case']
  });

  // 注册While循环节点
  registry.registerNodeType({
    type: 'logic/loop/while',
    category: NodeCategory.LOGIC,
    constructor: WhileLoopNode,
    label: 'While循环',
    description: '当条件为真时重复执行',
    icon: 'loop',
    color: '#9C27B0',
    tags: ['logic', 'loop', 'while', 'iteration']
  });

  // 注册ForEach循环节点
  registry.registerNodeType({
    type: 'logic/loop/forEach',
    category: NodeCategory.LOGIC,
    constructor: ForEachLoopNode,
    label: 'ForEach循环',
    description: '遍历数组或对象的每个元素',
    icon: 'forEach',
    color: '#9C27B0',
    tags: ['logic', 'loop', 'forEach', 'array']
  });

  // 注册状态机节点
  registry.registerNodeType({
    type: 'logic/flow/stateMachine',
    category: NodeCategory.LOGIC,
    constructor: StateMachineNode,
    label: '状态机',
    description: '实现有限状态机逻辑',
    icon: 'stateMachine',
    color: '#673AB7',
    tags: ['logic', 'flow', 'state', 'machine']
  });

  // 注册条件表达式节点
  registry.registerNodeType({
    type: 'logic/expression/condition',
    category: NodeCategory.LOGIC,
    constructor: ConditionExpressionNode,
    label: '条件表达式',
    description: '解析和执行字符串形式的条件表达式',
    icon: 'expression',
    color: '#4CAF50',
    tags: ['logic', 'expression', 'condition', 'parser']
  });

  // === 比较运算节点 ===

  // 注册相等比较节点
  registry.registerNodeType({
    type: 'logic/comparison/equal',
    category: NodeCategory.LOGIC,
    constructor: ComparisonNode,
    label: '相等',
    description: '比较两个值是否相等',
    icon: 'equal',
    color: '#FF9800',
    tags: ['logic', 'comparison', 'equal'],
    metadata: {
      operator: ComparisonOperator.EQUAL
    }
  });

  // 注册不相等比较节点
  registry.registerNodeType({
    type: 'logic/comparison/notEqual',
    category: NodeCategory.LOGIC,
    constructor: ComparisonNode,
    label: '不相等',
    description: '比较两个值是否不相等',
    icon: 'notEqual',
    color: '#FF9800',
    tags: ['logic', 'comparison', 'notEqual'],
    metadata: {
      operator: ComparisonOperator.NOT_EQUAL
    }
  });

  // 注册大于比较节点
  registry.registerNodeType({
    type: 'logic/comparison/greater',
    category: NodeCategory.LOGIC,
    constructor: ComparisonNode,
    label: '大于',
    description: '比较第一个值是否大于第二个值',
    icon: 'greater',
    color: '#FF9800',
    tags: ['logic', 'comparison', 'greater'],
    metadata: {
      operator: ComparisonOperator.GREATER
    }
  });

  // 注册大于等于比较节点
  registry.registerNodeType({
    type: 'logic/comparison/greaterEqual',
    category: NodeCategory.LOGIC,
    constructor: ComparisonNode,
    label: '大于等于',
    description: '比较第一个值是否大于等于第二个值',
    icon: 'greaterEqual',
    color: '#FF9800',
    tags: ['logic', 'comparison', 'greaterEqual'],
    metadata: {
      operator: ComparisonOperator.GREATER_EQUAL
    }
  });

  // 注册小于比较节点
  registry.registerNodeType({
    type: 'logic/comparison/less',
    category: NodeCategory.LOGIC,
    constructor: ComparisonNode,
    label: '小于',
    description: '比较第一个值是否小于第二个值',
    icon: 'less',
    color: '#FF9800',
    tags: ['logic', 'comparison', 'less'],
    metadata: {
      operator: ComparisonOperator.LESS
    }
  });

  // 注册小于等于比较节点
  registry.registerNodeType({
    type: 'logic/comparison/lessEqual',
    category: NodeCategory.LOGIC,
    constructor: ComparisonNode,
    label: '小于等于',
    description: '比较第一个值是否小于等于第二个值',
    icon: 'lessEqual',
    color: '#FF9800',
    tags: ['logic', 'comparison', 'lessEqual'],
    metadata: {
      operator: ComparisonOperator.LESS_EQUAL
    }
  });

  // 注册包含比较节点
  registry.registerNodeType({
    type: 'logic/comparison/contains',
    category: NodeCategory.LOGIC,
    constructor: ComparisonNode,
    label: '包含',
    description: '检查第一个值是否包含第二个值',
    icon: 'contains',
    color: '#FF9800',
    tags: ['logic', 'comparison', 'contains', 'string'],
    metadata: {
      operator: ComparisonOperator.CONTAINS
    }
  });

  // 注册开始于比较节点
  registry.registerNodeType({
    type: 'logic/comparison/startsWith',
    category: NodeCategory.LOGIC,
    constructor: ComparisonNode,
    label: '开始于',
    description: '检查第一个值是否以第二个值开始',
    icon: 'startsWith',
    color: '#FF9800',
    tags: ['logic', 'comparison', 'startsWith', 'string'],
    metadata: {
      operator: ComparisonOperator.STARTS_WITH
    }
  });

  // 注册结束于比较节点
  registry.registerNodeType({
    type: 'logic/comparison/endsWith',
    category: NodeCategory.LOGIC,
    constructor: ComparisonNode,
    label: '结束于',
    description: '检查第一个值是否以第二个值结束',
    icon: 'endsWith',
    color: '#FF9800',
    tags: ['logic', 'comparison', 'endsWith', 'string'],
    metadata: {
      operator: ComparisonOperator.ENDS_WITH
    }
  });

  // 注册正则匹配比较节点
  registry.registerNodeType({
    type: 'logic/comparison/regexMatch',
    category: NodeCategory.LOGIC,
    constructor: ComparisonNode,
    label: '正则匹配',
    description: '使用正则表达式匹配字符串',
    icon: 'regex',
    color: '#FF9800',
    tags: ['logic', 'comparison', 'regex', 'pattern'],
    metadata: {
      operator: ComparisonOperator.REGEX_MATCH
    }
  });

  // === 基础逻辑运算节点 ===

  // 注册与运算节点
  registry.registerNodeType({
    type: 'logic/operation/and',
    category: NodeCategory.LOGIC,
    constructor: LogicalOperationNode,
    label: '与',
    description: '执行逻辑与运算',
    icon: 'and',
    color: '#FF9800',
    tags: ['logic', 'operation', 'and'],
    metadata: {
      operator: LogicalOperator.AND
    }
  });

  // 注册或运算节点
  registry.registerNodeType({
    type: 'logic/operation/or',
    category: NodeCategory.LOGIC,
    constructor: LogicalOperationNode,
    label: '或',
    description: '执行逻辑或运算',
    icon: 'or',
    color: '#FF9800',
    tags: ['logic', 'operation', 'or'],
    metadata: {
      operator: LogicalOperator.OR
    }
  });

  // 注册非运算节点
  registry.registerNodeType({
    type: 'logic/operation/not',
    category: NodeCategory.LOGIC,
    constructor: LogicalOperationNode,
    label: '非',
    description: '执行逻辑非运算',
    icon: 'not',
    color: '#FF9800',
    tags: ['logic', 'operation', 'not'],
    metadata: {
      operator: LogicalOperator.NOT
    }
  });

  // === 扩展逻辑运算节点 ===

  // 注册异或运算节点
  registry.registerNodeType({
    type: 'logic/operation/xor',
    category: NodeCategory.LOGIC,
    constructor: ExtendedLogicalOperationNode,
    label: '异或',
    description: '执行逻辑异或运算',
    icon: 'xor',
    color: '#FF9800',
    tags: ['logic', 'operation', 'xor'],
    metadata: {
      operator: LogicalOperator.XOR
    }
  });

  // 注册与非运算节点
  registry.registerNodeType({
    type: 'logic/operation/nand',
    category: NodeCategory.LOGIC,
    constructor: ExtendedLogicalOperationNode,
    label: '与非',
    description: '执行逻辑与非运算',
    icon: 'nand',
    color: '#FF9800',
    tags: ['logic', 'operation', 'nand'],
    metadata: {
      operator: LogicalOperator.NAND
    }
  });

  // 注册或非运算节点
  registry.registerNodeType({
    type: 'logic/operation/nor',
    category: NodeCategory.LOGIC,
    constructor: ExtendedLogicalOperationNode,
    label: '或非',
    description: '执行逻辑或非运算',
    icon: 'nor',
    color: '#FF9800',
    tags: ['logic', 'operation', 'nor'],
    metadata: {
      operator: LogicalOperator.NOR
    }
  });

  // === 状态和控制节点 ===

  // 注册开关节点
  registry.registerNodeType({
    type: 'logic/flow/toggle',
    category: NodeCategory.LOGIC,
    constructor: ToggleNode,
    label: '开关',
    description: '在两个状态之间切换',
    icon: 'toggle',
    color: '#FF9800',
    tags: ['logic', 'flow', 'toggle']
  });

  console.log('[逻辑节点] 已注册所有逻辑节点类型');
}
