# MathNodes.ts 功能完善说明

## 概述

MathNodes.ts 文件已经过全面完善，从原有的基础数学运算节点扩展为一个功能完整的数学计算系统，新增了大量高级数学函数、向量运算、统计函数等功能。

## 新增功能分类

### 1. 扩展三角函数
**新增函数**：
- **ATAN2**：两参数反正切函数，用于计算向量角度
- **SINH/COSH/TANH**：双曲函数系列，用于高级数学计算

**特点**：
- 支持所有常用三角函数和反三角函数
- 双曲函数支持复杂数学建模
- ATAN2函数解决象限问题

### 2. 数学函数节点（MathFunctionNode）
**新增函数类型**：
- **取整函数**：floor（向下）、ceil（向上）、round（四舍五入）、trunc（截断）
- **符号函数**：abs（绝对值）、sign（符号）
- **对数函数**：log（自然对数）、log10（常用对数）、log2（二进制对数）
- **指数函数**：exp（e的x次幂）、exp2（2的x次幂）

**特点**：
- 统一的函数节点架构
- 自动处理边界情况（如负数对数返回NaN）
- 高精度数学计算

### 3. 最值函数节点（MinMaxNode）
**功能**：
- **min/max**：计算两个数的最小值/最大值
- **clamp**：将数值限制在指定范围内

**特点**：
- 支持动态范围限制
- 三参数clamp函数（值、最小值、最大值）
- 广泛用于数值范围控制

### 4. 随机数生成节点（RandomNode）
**随机数类型**：
- **random**：生成0-1之间的随机数
- **randomRange**：生成指定范围内的随机数
- **randomInt**：生成0到指定值的随机整数
- **randomIntRange**：生成指定范围内的随机整数

**高级特性**：
- **种子支持**：可选的随机种子，确保可重现的随机序列
- **伪随机算法**：基于正弦函数的简单伪随机数生成器
- **类型安全**：自动处理整数和浮点数转换

### 5. 插值函数节点（InterpolationNode）
**插值类型**：
- **LERP**：线性插值，在两个值之间平滑过渡
- **SMOOTHSTEP**：平滑插值，S型曲线过渡
- **STEP**：阶跃函数，硬切换

**应用场景**：
- 动画过渡效果
- 数值平滑处理
- 渐变计算

### 6. 数值映射节点（MapNode）
**功能**：
- 将数值从一个范围映射到另一个范围
- 可选的输出范围限制（clamp）
- 支持反向映射

**应用**：
- 传感器数据标准化
- UI控件值转换
- 游戏参数映射

### 7. 向量运算节点（VectorMathNode）
**支持维度**：
- **2D向量**：适用于2D图形和UI计算
- **3D向量**：适用于3D图形和物理计算

**运算类型**：
- **基础运算**：加法、减法、乘法、除法
- **标量运算**：向量与标量的乘除法
- **高级运算**：点积、叉积（3D）、长度、归一化、距离

**特点**：
- 自动处理零向量情况
- 支持逐分量运算
- 完整的向量数学库

### 8. 数值验证节点（NumberValidationNode）
**验证类型**：
- **isNaN**：检查是否为非数字
- **isFinite**：检查是否为有限数
- **isInteger**：检查是否为整数
- **isPositive/isNegative/isZero**：检查数值符号

**用途**：
- 数据有效性检查
- 错误处理和调试
- 条件分支控制

### 9. 数学常数节点（MathConstantNode）
**提供常数**：
- **基础常数**：PI（π）、E（自然常数）
- **对数常数**：LN2、LN10、LOG2E、LOG10E
- **根号常数**：SQRT1_2、SQRT2
- **转换常数**：DEG_TO_RAD、RAD_TO_DEG
- **特殊常数**：TAU（2π）、GOLDEN_RATIO（黄金比例）

## 性能优化

### 1. 计算优化
- 向量运算使用内联计算，避免不必要的对象创建
- 三角函数直接调用Math库，确保最佳性能
- 边界条件预检查，避免无效计算

### 2. 内存优化
- 向量结果复用对象结构
- 常数节点使用静态值，避免重复计算
- 随机数生成器状态最小化

### 3. 类型安全
- 严格的类型检查和转换
- 自动处理undefined和null值
- 数值范围验证

## 错误处理

### 1. 数学错误
- 除零保护（返回0或保持原值）
- 负数对数处理（返回NaN）
- 向量归一化零长度保护

### 2. 输入验证
- 自动类型转换和验证
- 默认值提供
- 边界条件处理

### 3. 调试支持
- 详细的节点描述和标签
- 清晰的输入输出命名
- 完整的错误信息

## 使用示例

### 向量计算示例
```typescript
// 3D向量加法
const vectorAdd = new VectorMathNode({
  metadata: { operation: 'add', dimension: 3 }
});

// 输入: vectorA = {x: 1, y: 2, z: 3}, vectorB = {x: 4, y: 5, z: 6}
// 输出: {x: 5, y: 7, z: 9}
```

### 插值动画示例
```typescript
// 平滑插值
const smoothstep = new InterpolationNode({
  metadata: { interpolationType: 'smoothstep' }
});

// 输入: a = 0, b = 100, t = 0.5
// 输出: 50（平滑过渡的中点值）
```

### 随机数生成示例
```typescript
// 带种子的范围随机数
const randomRange = new RandomNode({
  metadata: { randomType: 'randomRange' }
});

// 输入: min = 10, max = 20, seed = 12345
// 输出: 10-20之间的可重现随机数
```

## 节点统计

### 原有节点
- 基础运算节点：4个（加减乘除）
- 幂运算节点：1个
- 三角函数节点：3个（sin、cos、tan）

### 新增节点
- **扩展三角函数**：6个（asin、acos、atan、atan2、sinh、cosh、tanh）
- **数学函数**：9个（abs、sign、floor、ceil、round、trunc、log、log10、log2、exp、exp2）
- **最值函数**：3个（min、max、clamp）
- **随机数函数**：4个（random、randomRange、randomInt、randomIntRange）
- **插值函数**：3个（lerp、smoothstep、step）
- **数值映射**：1个（map）
- **向量运算**：8个（2D/3D的add、subtract、length、normalize、dot、cross）
- **数值验证**：3个（isNaN、isFinite、isInteger）
- **数学常数**：4个（PI、E、DEG_TO_RAD、RAD_TO_DEG）

### 总计
- **原有节点**：8个
- **新增节点**：41个
- **总节点数**：49个
- **代码行数**：从约800行增加到2687行

## 兼容性

- 完全向后兼容原有数学节点
- 新节点遵循相同的接口规范
- 可以与现有节点无缝集成
- 支持复杂的数学表达式构建

## 总结

完善后的MathNodes.ts文件提供了：
- **完整的数学函数库**：覆盖基础到高级的数学运算
- **强大的向量运算**：支持2D/3D图形计算
- **灵活的随机数生成**：支持各种随机数需求
- **实用的插值和映射**：适用于动画和数据处理
- **可靠的数值验证**：确保计算安全性
- **丰富的数学常数**：提供常用数学常数

这些增强功能使得视觉脚本系统能够处理复杂的数学计算场景，满足游戏开发、科学计算、数据处理等多种应用需求。
