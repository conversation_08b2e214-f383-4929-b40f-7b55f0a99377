/**
 * 时间相关的可视化脚本节点
 */

import { VisualScriptNode } from '../VisualScriptNode';
import { NodeRegistry } from '../NodeRegistry';
import { Time } from '../../utils/Time';

/**
 * 获取当前时间节点
 */
export class GetTimeNode extends VisualScriptNode {
  constructor() {
    super('GetTime', '获取时间');
    this.addOutput('time', 'number', '当前时间(毫秒)');
    this.addOutput('gameTime', 'number', '游戏时间(秒)');
    this.addOutput('realTime', 'number', '真实时间(秒)');
    this.addOutput('deltaTime', 'number', '帧时间(秒)');
    this.addOutput('unscaledDeltaTime', 'number', '未缩放帧时间(秒)');
    this.addOutput('timeScale', 'number', '时间缩放');
    this.addOutput('frameCount', 'number', '帧数');
    this.addOutput('fps', 'number', 'FPS');
  }

  public execute(): any {
    const now = performance.now();
    const deltaTime = this.getContext()?.deltaTime || Time.getDeltaTime();

    return {
      time: now,
      gameTime: Time.getTime(),
      realTime: Time.getRealTime(),
      deltaTime: deltaTime,
      unscaledDeltaTime: Time.getUnscaledDeltaTime(),
      timeScale: Time.getTimeScale(),
      frameCount: Time.getFrameCount(),
      fps: Time.getFPS()
    };
  }
}

/**
 * 延迟节点
 */
export class DelayNode extends VisualScriptNode {
  private startTime: number = 0;
  private isWaiting: boolean = false;
  private timerId: any = null;

  constructor() {
    super('Delay', '延迟');
    this.addInput('trigger', 'exec', '触发');
    this.addInput('duration', 'number', '延迟时间(毫秒)');
    this.addInput('reset', 'exec', '重置');
    this.addOutput('completed', 'exec', '完成');
    this.addOutput('progress', 'number', '进度(0-1)');
    this.addOutput('remaining', 'number', '剩余时间(毫秒)');
  }

  public execute(inputs: any): any {
    // 重置延迟
    if (inputs.reset) {
      this.reset();
      return { progress: 0, remaining: 0 };
    }

    // 开始延迟
    if (inputs.trigger && !this.isWaiting) {
      this.startTime = performance.now();
      this.isWaiting = true;

      // 清除之前的定时器
      if (this.timerId) {
        clearTimeout(this.timerId);
      }
    }

    if (this.isWaiting) {
      const elapsed = performance.now() - this.startTime;
      const duration = Math.max(0, inputs.duration || 1000);
      const progress = Math.min(1, elapsed / duration);
      const remaining = Math.max(0, duration - elapsed);

      if (elapsed >= duration) {
        this.isWaiting = false;
        return {
          completed: true,
          progress: 1,
          remaining: 0
        };
      }

      return {
        progress: progress,
        remaining: remaining
      };
    }

    return { progress: 0, remaining: 0 };
  }

  private reset(): void {
    this.isWaiting = false;
    this.startTime = 0;
    if (this.timerId) {
      clearTimeout(this.timerId);
      this.timerId = null;
    }
  }
}

/**
 * 计时器节点
 */
export class TimerNode extends VisualScriptNode {
  private startTime: number = 0;
  private pausedTime: number = 0;
  private totalPausedTime: number = 0;
  private isRunning: boolean = false;
  private isPaused: boolean = false;

  constructor() {
    super('Timer', '计时器');
    this.addInput('start', 'exec', '开始');
    this.addInput('stop', 'exec', '停止');
    this.addInput('pause', 'exec', '暂停');
    this.addInput('resume', 'exec', '恢复');
    this.addInput('reset', 'exec', '重置');
    this.addOutput('elapsed', 'number', '已用时间(毫秒)');
    this.addOutput('elapsedSeconds', 'number', '已用时间(秒)');
    this.addOutput('isRunning', 'boolean', '是否运行中');
    this.addOutput('isPaused', 'boolean', '是否暂停');
  }

  public execute(inputs: any): any {
    const now = performance.now();

    // 开始计时
    if (inputs.start && !this.isRunning) {
      this.startTime = now;
      this.totalPausedTime = 0;
      this.isRunning = true;
      this.isPaused = false;
    }

    // 停止计时
    if (inputs.stop) {
      this.isRunning = false;
      this.isPaused = false;
    }

    // 暂停计时
    if (inputs.pause && this.isRunning && !this.isPaused) {
      this.pausedTime = now;
      this.isPaused = true;
    }

    // 恢复计时
    if (inputs.resume && this.isRunning && this.isPaused) {
      this.totalPausedTime += now - this.pausedTime;
      this.isPaused = false;
    }

    // 重置计时器
    if (inputs.reset) {
      this.startTime = now;
      this.totalPausedTime = 0;
      this.isRunning = false;
      this.isPaused = false;
    }

    // 计算已用时间
    let elapsed = 0;
    if (this.isRunning) {
      if (this.isPaused) {
        elapsed = this.pausedTime - this.startTime - this.totalPausedTime;
      } else {
        elapsed = now - this.startTime - this.totalPausedTime;
      }
    }

    return {
      elapsed: Math.max(0, elapsed),
      elapsedSeconds: Math.max(0, elapsed / 1000),
      isRunning: this.isRunning,
      isPaused: this.isPaused
    };
  }
}

/**
 * 间隔执行节点
 */
export class IntervalNode extends VisualScriptNode {
  private intervalId: any = null;
  private isRunning: boolean = false;
  private executionCount: number = 0;

  constructor() {
    super('Interval', '间隔执行');
    this.addInput('start', 'exec', '开始');
    this.addInput('stop', 'exec', '停止');
    this.addInput('interval', 'number', '间隔时间(毫秒)');
    this.addInput('maxExecutions', 'number', '最大执行次数(0=无限)');
    this.addOutput('tick', 'exec', '执行');
    this.addOutput('count', 'number', '执行次数');
    this.addOutput('isRunning', 'boolean', '是否运行中');
  }

  public execute(inputs: any): any {
    // 开始间隔执行
    if (inputs.start && !this.isRunning) {
      const interval = Math.max(1, inputs.interval || 1000);
      const maxExecutions = inputs.maxExecutions || 0;

      this.isRunning = true;
      this.executionCount = 0;

      this.intervalId = setInterval(() => {
        this.executionCount++;

        // 检查是否达到最大执行次数
        if (maxExecutions > 0 && this.executionCount >= maxExecutions) {
          this.stop();
        }
      }, interval);
    }

    // 停止间隔执行
    if (inputs.stop) {
      this.stop();
    }

    return {
      count: this.executionCount,
      isRunning: this.isRunning,
      tick: this.isRunning
    };
  }

  private stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    this.isRunning = false;
  }
}

/**
 * 时间比较节点
 */
export class TimeCompareNode extends VisualScriptNode {
  constructor() {
    super('TimeCompare', '时间比较');
    this.addInput('timeA', 'number', '时间A');
    this.addInput('timeB', 'number', '时间B');
    this.addInput('tolerance', 'number', '容差(毫秒)');
    this.addOutput('isEqual', 'boolean', '相等');
    this.addOutput('isGreater', 'boolean', 'A > B');
    this.addOutput('isLess', 'boolean', 'A < B');
    this.addOutput('difference', 'number', '差值(毫秒)');
  }

  public execute(inputs: any): any {
    const timeA = inputs.timeA || 0;
    const timeB = inputs.timeB || 0;
    const tolerance = Math.abs(inputs.tolerance || 0);
    const difference = timeA - timeB;
    const absDifference = Math.abs(difference);

    return {
      isEqual: absDifference <= tolerance,
      isGreater: difference > tolerance,
      isLess: difference < -tolerance,
      difference: difference
    };
  }
}

/**
 * 时间格式化节点
 */
export class TimeFormatNode extends VisualScriptNode {
  constructor() {
    super('TimeFormat', '时间格式化');
    this.addInput('time', 'number', '时间(毫秒)');
    this.addInput('format', 'string', '格式("HH:MM:SS", "MM:SS", "seconds")');
    this.addOutput('formatted', 'string', '格式化时间');
    this.addOutput('hours', 'number', '小时');
    this.addOutput('minutes', 'number', '分钟');
    this.addOutput('seconds', 'number', '秒');
    this.addOutput('milliseconds', 'number', '毫秒');
  }

  public execute(inputs: any): any {
    const time = Math.max(0, inputs.time || 0);
    const format = inputs.format || 'MM:SS';

    const totalSeconds = Math.floor(time / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    const milliseconds = Math.floor(time % 1000);

    let formatted = '';
    switch (format.toLowerCase()) {
      case 'hh:mm:ss':
        formatted = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        break;
      case 'mm:ss':
        formatted = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        break;
      case 'seconds':
        formatted = (time / 1000).toFixed(2);
        break;
      default:
        formatted = `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }

    return {
      formatted,
      hours,
      minutes,
      seconds,
      milliseconds
    };
  }
}

/**
 * 时间缩放控制节点
 */
export class TimeScaleNode extends VisualScriptNode {
  constructor() {
    super('TimeScale', '时间缩放');
    this.addInput('trigger', 'exec', '触发');
    this.addInput('scale', 'number', '缩放值');
    this.addInput('duration', 'number', '过渡时间(毫秒)');
    this.addOutput('completed', 'exec', '完成');
    this.addOutput('currentScale', 'number', '当前缩放');
  }

  public execute(inputs: any): any {
    if (inputs.trigger) {
      const targetScale = Math.max(0, inputs.scale || 1);
      const duration = inputs.duration || 0;

      if (duration > 0) {
        // 平滑过渡到目标缩放
        this.smoothTransition(targetScale, duration);
      } else {
        // 立即设置
        Time.setTimeScale(targetScale);
        return {
          completed: true,
          currentScale: targetScale
        };
      }
    }

    return {
      currentScale: Time.getTimeScale()
    };
  }

  private smoothTransition(targetScale: number, duration: number): void {
    const startScale = Time.getTimeScale();
    const startTime = performance.now();

    const animate = () => {
      const elapsed = performance.now() - startTime;
      const progress = Math.min(1, elapsed / duration);

      // 使用缓入缓出插值
      const easeProgress = progress < 0.5
        ? 2 * progress * progress
        : 1 - Math.pow(-2 * progress + 2, 2) / 2;

      const currentScale = startScale + (targetScale - startScale) * easeProgress;
      Time.setTimeScale(currentScale);

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    requestAnimationFrame(animate);
  }
}

/**
 * 时间插值节点
 */
export class TimeInterpolateNode extends VisualScriptNode {
  constructor() {
    super('TimeInterpolate', '时间插值');
    this.addInput('from', 'number', '起始值');
    this.addInput('to', 'number', '目标值');
    this.addInput('duration', 'number', '持续时间(毫秒)');
    this.addInput('easing', 'string', '缓动类型');
    this.addInput('trigger', 'exec', '触发');
    this.addInput('reset', 'exec', '重置');
    this.addOutput('value', 'number', '当前值');
    this.addOutput('progress', 'number', '进度(0-1)');
    this.addOutput('completed', 'exec', '完成');
    this.addOutput('isRunning', 'boolean', '是否运行中');
  }

  private startTime: number = 0;
  private isRunning: boolean = false;
  private fromValue: number = 0;
  private toValue: number = 1;
  private duration: number = 1000;

  public execute(inputs: any): any {
    // 触发插值
    if (inputs.trigger && !this.isRunning) {
      this.startTime = performance.now();
      this.isRunning = true;
      this.fromValue = inputs.from || 0;
      this.toValue = inputs.to || 1;
      this.duration = Math.max(1, inputs.duration || 1000);
    }

    // 重置插值
    if (inputs.reset) {
      this.isRunning = false;
      this.startTime = 0;
      return {
        value: this.fromValue,
        progress: 0,
        isRunning: false
      };
    }

    if (this.isRunning) {
      const elapsed = performance.now() - this.startTime;
      let progress = Math.min(1, elapsed / this.duration);

      // 应用缓动函数
      const easingType = inputs.easing || 'linear';
      progress = this.applyEasing(progress, easingType);

      const currentValue = this.fromValue + (this.toValue - this.fromValue) * progress;

      if (elapsed >= this.duration) {
        this.isRunning = false;
        return {
          value: this.toValue,
          progress: 1,
          completed: true,
          isRunning: false
        };
      }

      return {
        value: currentValue,
        progress: progress,
        isRunning: true
      };
    }

    return {
      value: this.fromValue,
      progress: 0,
      isRunning: false
    };
  }

  private applyEasing(t: number, type: string): number {
    switch (type.toLowerCase()) {
      case 'linear':
        return t;
      case 'easein':
        return t * t;
      case 'easeout':
        return t * (2 - t);
      case 'easeinout':
        return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
      case 'bounce':
        if (t < 1 / 2.75) {
          return 7.5625 * t * t;
        } else if (t < 2 / 2.75) {
          return 7.5625 * (t -= 1.5 / 2.75) * t + 0.75;
        } else if (t < 2.5 / 2.75) {
          return 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375;
        } else {
          return 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375;
        }
      default:
        return t;
    }
  }
}

/**
 * 时间事件调度节点
 */
export class TimeSchedulerNode extends VisualScriptNode {
  private events: Array<{
    id: string;
    time: number;
    executed: boolean;
    data: any;
  }> = [];
  private startTime: number = 0;
  private isRunning: boolean = false;

  constructor() {
    super('TimeScheduler', '时间调度器');
    this.addInput('start', 'exec', '开始');
    this.addInput('stop', 'exec', '停止');
    this.addInput('reset', 'exec', '重置');
    this.addInput('addEvent', 'exec', '添加事件');
    this.addInput('eventTime', 'number', '事件时间(毫秒)');
    this.addInput('eventData', 'any', '事件数据');
    this.addOutput('eventTriggered', 'exec', '事件触发');
    this.addOutput('eventData', 'any', '事件数据');
    this.addOutput('currentTime', 'number', '当前时间');
    this.addOutput('isRunning', 'boolean', '是否运行中');
  }

  public execute(inputs: any): any {
    const now = performance.now();

    // 开始调度
    if (inputs.start && !this.isRunning) {
      this.startTime = now;
      this.isRunning = true;
      // 重置所有事件的执行状态
      this.events.forEach(event => event.executed = false);
    }

    // 停止调度
    if (inputs.stop) {
      this.isRunning = false;
    }

    // 重置调度器
    if (inputs.reset) {
      this.isRunning = false;
      this.events = [];
      this.startTime = 0;
    }

    // 添加事件
    if (inputs.addEvent && inputs.eventTime !== undefined) {
      const eventId = `event_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      this.events.push({
        id: eventId,
        time: inputs.eventTime,
        executed: false,
        data: inputs.eventData
      });
    }

    if (this.isRunning) {
      const currentTime = now - this.startTime;

      // 检查是否有事件需要触发
      for (const event of this.events) {
        if (!event.executed && currentTime >= event.time) {
          event.executed = true;
          return {
            eventTriggered: true,
            eventData: event.data,
            currentTime: currentTime,
            isRunning: this.isRunning
          };
        }
      }

      return {
        currentTime: currentTime,
        isRunning: this.isRunning
      };
    }

    return {
      currentTime: 0,
      isRunning: false
    };
  }
}

/**
 * 注册时间节点
 */
export function registerTimeNodes(): void {
  NodeRegistry.register('GetTime', GetTimeNode);
  NodeRegistry.register('Delay', DelayNode);
  NodeRegistry.register('Timer', TimerNode);
  NodeRegistry.register('Interval', IntervalNode);
  NodeRegistry.register('TimeCompare', TimeCompareNode);
  NodeRegistry.register('TimeFormat', TimeFormatNode);
  NodeRegistry.register('TimeScale', TimeScaleNode);
  NodeRegistry.register('TimeInterpolate', TimeInterpolateNode);
  NodeRegistry.register('TimeScheduler', TimeSchedulerNode);
}
