/**
 * 输入相关的可视化脚本节点
 * 提供键盘、鼠标、触摸、游戏手柄等输入设备的支持
 */

import { FunctionNode } from '../nodes/FunctionNode';
import { FlowNode } from '../nodes/FlowNode';
import { AsyncNode, AsyncNodeOptions } from '../nodes/AsyncNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 输入设备类型
 */
export enum InputDeviceType {
  KEYBOARD = 'keyboard',
  MOUSE = 'mouse',
  TOUCH = 'touch',
  GAMEPAD = 'gamepad',
  ACCELEROMETER = 'accelerometer',
  GYROSCOPE = 'gyroscope'
}

/**
 * 输入事件类型
 */
export enum InputEventType {
  KEY_DOWN = 'keydown',
  KEY_UP = 'keyup',
  KEY_PRESS = 'keypress',
  MOUSE_DOWN = 'mousedown',
  MOUSE_UP = 'mouseup',
  MOUSE_MOVE = 'mousemove',
  MOUSE_WHEEL = 'wheel',
  TOUCH_START = 'touchstart',
  TOUCH_END = 'touchend',
  TOUCH_MOVE = 'touchmove',
  GAMEPAD_CONNECTED = 'gamepadconnected',
  GAMEPAD_DISCONNECTED = 'gamepaddisconnected'
}

/**
 * 鼠标按钮枚举
 */
export enum MouseButton {
  LEFT = 0,
  MIDDLE = 1,
  RIGHT = 2,
  BACK = 3,
  FORWARD = 4
}

/**
 * 游戏手柄按钮映射
 */
export enum GamepadButton {
  A = 0,
  B = 1,
  X = 2,
  Y = 3,
  LEFT_BUMPER = 4,
  RIGHT_BUMPER = 5,
  LEFT_TRIGGER = 6,
  RIGHT_TRIGGER = 7,
  SELECT = 8,
  START = 9,
  LEFT_STICK = 10,
  RIGHT_STICK = 11,
  DPAD_UP = 12,
  DPAD_DOWN = 13,
  DPAD_LEFT = 14,
  DPAD_RIGHT = 15
}

/**
 * 输入状态接口
 */
export interface InputState {
  pressed: boolean;
  justPressed: boolean;
  justReleased: boolean;
  pressTime: number;
  releaseTime: number;
}

/**
 * 触摸点信息
 */
export interface TouchPoint {
  id: number;
  x: number;
  y: number;
  pressure: number;
  radiusX: number;
  radiusY: number;
}

/**
 * 输入管理器
 */
export class InputManager extends EventEmitter {
  private static instance: InputManager;
  private keyStates: Map<string, InputState> = new Map();
  private mouseStates: Map<number, InputState> = new Map();
  private mousePosition: { x: number; y: number } = { x: 0, y: 0 };
  private mouseDelta: { x: number; y: number } = { x: 0, y: 0 };
  private touchPoints: Map<number, TouchPoint> = new Map();
  private gamepads: Map<number, Gamepad> = new Map();
  private isInitialized: boolean = false;

  public static getInstance(): InputManager {
    if (!InputManager.instance) {
      InputManager.instance = new InputManager();
    }
    return InputManager.instance;
  }

  public initialize(): void {
    if (this.isInitialized) return;

    this.setupKeyboardListeners();
    this.setupMouseListeners();
    this.setupTouchListeners();
    this.setupGamepadListeners();

    this.isInitialized = true;
  }

  private setupKeyboardListeners(): void {
    if (typeof window === 'undefined') return;

    window.addEventListener('keydown', (event) => {
      this.updateKeyState(event.code, true);
      this.emit(InputEventType.KEY_DOWN, event);
    });

    window.addEventListener('keyup', (event) => {
      this.updateKeyState(event.code, false);
      this.emit(InputEventType.KEY_UP, event);
    });
  }

  private setupMouseListeners(): void {
    if (typeof window === 'undefined') return;

    window.addEventListener('mousedown', (event) => {
      this.updateMouseState(event.button, true);
      this.emit(InputEventType.MOUSE_DOWN, event);
    });

    window.addEventListener('mouseup', (event) => {
      this.updateMouseState(event.button, false);
      this.emit(InputEventType.MOUSE_UP, event);
    });

    window.addEventListener('mousemove', (event) => {
      this.updateMousePosition(event.clientX, event.clientY);
      this.emit(InputEventType.MOUSE_MOVE, event);
    });

    window.addEventListener('wheel', (event) => {
      this.emit(InputEventType.MOUSE_WHEEL, event);
    });
  }

  private setupTouchListeners(): void {
    if (typeof window === 'undefined') return;

    window.addEventListener('touchstart', (event) => {
      this.updateTouchPoints(event.touches);
      this.emit(InputEventType.TOUCH_START, event);
    });

    window.addEventListener('touchend', (event) => {
      this.updateTouchPoints(event.touches);
      this.emit(InputEventType.TOUCH_END, event);
    });

    window.addEventListener('touchmove', (event) => {
      this.updateTouchPoints(event.touches);
      this.emit(InputEventType.TOUCH_MOVE, event);
    });
  }

  private setupGamepadListeners(): void {
    if (typeof window === 'undefined') return;

    window.addEventListener('gamepadconnected', (event) => {
      this.gamepads.set(event.gamepad.index, event.gamepad);
      this.emit(InputEventType.GAMEPAD_CONNECTED, event);
    });

    window.addEventListener('gamepaddisconnected', (event) => {
      this.gamepads.delete(event.gamepad.index);
      this.emit(InputEventType.GAMEPAD_DISCONNECTED, event);
    });
  }

  private updateKeyState(key: string, pressed: boolean): void {
    const currentState = this.keyStates.get(key) || {
      pressed: false,
      justPressed: false,
      justReleased: false,
      pressTime: 0,
      releaseTime: 0
    };

    const now = Date.now();
    const wasPressed = currentState.pressed;

    currentState.pressed = pressed;
    currentState.justPressed = pressed && !wasPressed;
    currentState.justReleased = !pressed && wasPressed;

    if (pressed && !wasPressed) {
      currentState.pressTime = now;
    } else if (!pressed && wasPressed) {
      currentState.releaseTime = now;
    }

    this.keyStates.set(key, currentState);
  }

  private updateMouseState(button: number, pressed: boolean): void {
    const currentState = this.mouseStates.get(button) || {
      pressed: false,
      justPressed: false,
      justReleased: false,
      pressTime: 0,
      releaseTime: 0
    };

    const now = Date.now();
    const wasPressed = currentState.pressed;

    currentState.pressed = pressed;
    currentState.justPressed = pressed && !wasPressed;
    currentState.justReleased = !pressed && wasPressed;

    if (pressed && !wasPressed) {
      currentState.pressTime = now;
    } else if (!pressed && wasPressed) {
      currentState.releaseTime = now;
    }

    this.mouseStates.set(button, currentState);
  }

  private updateMousePosition(x: number, y: number): void {
    this.mouseDelta.x = x - this.mousePosition.x;
    this.mouseDelta.y = y - this.mousePosition.y;
    this.mousePosition.x = x;
    this.mousePosition.y = y;
  }

  private updateTouchPoints(touches: TouchList): void {
    this.touchPoints.clear();

    for (let i = 0; i < touches.length; i++) {
      const touch = touches[i];
      this.touchPoints.set(touch.identifier, {
        id: touch.identifier,
        x: touch.clientX,
        y: touch.clientY,
        pressure: touch.force || 1.0,
        radiusX: touch.radiusX || 0,
        radiusY: touch.radiusY || 0
      });
    }
  }

  public getKeyState(key: string): InputState | undefined {
    return this.keyStates.get(key);
  }

  public getMouseState(button: number): InputState | undefined {
    return this.mouseStates.get(button);
  }

  public getMousePosition(): { x: number; y: number } {
    return { ...this.mousePosition };
  }

  public getMouseDelta(): { x: number; y: number } {
    return { ...this.mouseDelta };
  }

  public getTouchPoints(): TouchPoint[] {
    return Array.from(this.touchPoints.values());
  }

  public getGamepad(index: number): Gamepad | undefined {
    // 更新游戏手柄状态
    if (typeof navigator !== 'undefined' && navigator.getGamepads) {
      const gamepads = navigator.getGamepads();
      if (gamepads[index]) {
        this.gamepads.set(index, gamepads[index]);
        return gamepads[index];
      }
    }
    return this.gamepads.get(index);
  }

  public clearJustPressed(): void {
    // 清除"刚按下"和"刚释放"状态，通常在每帧结束时调用
    for (const state of this.keyStates.values()) {
      state.justPressed = false;
      state.justReleased = false;
    }

    for (const state of this.mouseStates.values()) {
      state.justPressed = false;
      state.justReleased = false;
    }
  }
}

/**
 * 键盘输入节点
 */
export class KeyboardInputNode extends FunctionNode {
  private inputManager: InputManager;

  constructor(options: any) {
    super(options);
    this.inputManager = InputManager.getInstance();
    this.inputManager.initialize();
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入插槽
    this.addInput({
      name: 'key',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '按键代码（如：KeyA, Space, Enter）',
      defaultValue: 'KeyA'
    });

    this.addInput({
      name: 'checkModifiers',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否检查修饰键',
      defaultValue: false,
      optional: true
    });

    // 添加输出插槽
    this.addOutput({
      name: 'pressed',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否按下'
    });

    this.addOutput({
      name: 'justPressed',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '刚按下'
    });

    this.addOutput({
      name: 'justReleased',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '刚释放'
    });

    this.addOutput({
      name: 'pressTime',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '按下时间戳'
    });

    this.addOutput({
      name: 'holdDuration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '按住持续时间（毫秒）'
    });
  }

  /**
   * 执行节点
   */
  public execute(): any {
    const key = this.getInputValue('key') as string;
    const checkModifiers = this.getInputValue('checkModifiers') as boolean;

    if (!key) {
      this.setAllOutputsToDefault();
      return null;
    }

    const keyState = this.inputManager.getKeyState(key);

    if (!keyState) {
      this.setAllOutputsToDefault();
      return null;
    }

    // 计算按住持续时间
    const holdDuration = keyState.pressed ? Date.now() - keyState.pressTime : 0;

    // 设置输出值
    this.setOutputValue('pressed', keyState.pressed);
    this.setOutputValue('justPressed', keyState.justPressed);
    this.setOutputValue('justReleased', keyState.justReleased);
    this.setOutputValue('pressTime', keyState.pressTime);
    this.setOutputValue('holdDuration', holdDuration);

    return {
      pressed: keyState.pressed,
      justPressed: keyState.justPressed,
      justReleased: keyState.justReleased,
      pressTime: keyState.pressTime,
      holdDuration
    };
  }

  private setAllOutputsToDefault(): void {
    this.setOutputValue('pressed', false);
    this.setOutputValue('justPressed', false);
    this.setOutputValue('justReleased', false);
    this.setOutputValue('pressTime', 0);
    this.setOutputValue('holdDuration', 0);
  }
}

/**
 * 鼠标输入节点
 */
export class MouseInputNode extends FunctionNode {
  private inputManager: InputManager;

  constructor(options: any) {
    super(options);
    this.inputManager = InputManager.getInstance();
    this.inputManager.initialize();
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入插槽
    this.addInput({
      name: 'button',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '鼠标按钮（0=左键, 1=中键, 2=右键）',
      defaultValue: MouseButton.LEFT,
      optional: true
    });

    this.addInput({
      name: 'relative',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否返回相对坐标',
      defaultValue: false,
      optional: true
    });

    // 添加输出插槽
    this.addOutput({
      name: 'pressed',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否按下'
    });

    this.addOutput({
      name: 'justPressed',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '刚按下'
    });

    this.addOutput({
      name: 'justReleased',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '刚释放'
    });

    this.addOutput({
      name: 'position',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '鼠标位置'
    });

    this.addOutput({
      name: 'delta',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '移动增量'
    });

    this.addOutput({
      name: 'leftButton',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '左键状态'
    });

    this.addOutput({
      name: 'rightButton',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '右键状态'
    });

    this.addOutput({
      name: 'middleButton',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '中键状态'
    });
  }

  /**
   * 执行节点
   */
  public execute(): any {
    const button = this.getInputValue('button') as number;
    const relative = this.getInputValue('relative') as boolean;

    // 获取鼠标状态
    const buttonState = this.inputManager.getMouseState(button);
    const position = this.inputManager.getMousePosition();
    const delta = this.inputManager.getMouseDelta();

    // 获取所有按钮状态
    const leftState = this.inputManager.getMouseState(MouseButton.LEFT);
    const rightState = this.inputManager.getMouseState(MouseButton.RIGHT);
    const middleState = this.inputManager.getMouseState(MouseButton.MIDDLE);

    // 设置输出值
    this.setOutputValue('pressed', buttonState?.pressed || false);
    this.setOutputValue('justPressed', buttonState?.justPressed || false);
    this.setOutputValue('justReleased', buttonState?.justReleased || false);
    this.setOutputValue('position', position);
    this.setOutputValue('delta', delta);
    this.setOutputValue('leftButton', leftState?.pressed || false);
    this.setOutputValue('rightButton', rightState?.pressed || false);
    this.setOutputValue('middleButton', middleState?.pressed || false);

    return {
      pressed: buttonState?.pressed || false,
      justPressed: buttonState?.justPressed || false,
      justReleased: buttonState?.justReleased || false,
      position,
      delta,
      leftButton: leftState?.pressed || false,
      rightButton: rightState?.pressed || false,
      middleButton: middleState?.pressed || false
    };
  }
}

/**
 * 触摸输入节点
 */
export class TouchInputNode extends FunctionNode {
  private inputManager: InputManager;

  constructor(options: any) {
    super(options);
    this.inputManager = InputManager.getInstance();
    this.inputManager.initialize();
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入插槽
    this.addInput({
      name: 'touchIndex',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '触摸点索引（-1表示第一个触摸点）',
      defaultValue: -1,
      optional: true
    });

    this.addInput({
      name: 'maxTouches',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '最大触摸点数量',
      defaultValue: 10,
      optional: true
    });

    // 添加输出插槽
    this.addOutput({
      name: 'touching',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否有触摸'
    });

    this.addOutput({
      name: 'touchCount',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '触摸点数量'
    });

    this.addOutput({
      name: 'position',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '主触摸点位置'
    });

    this.addOutput({
      name: 'allTouches',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '所有触摸点信息'
    });

    this.addOutput({
      name: 'averagePosition',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '平均触摸位置'
    });

    this.addOutput({
      name: 'pinchDistance',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '双指间距离'
    });

    this.addOutput({
      name: 'isPinching',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否在捏合'
    });
  }

  /**
   * 执行节点
   */
  public execute(): any {
    const touchIndex = this.getInputValue('touchIndex') as number;
    const maxTouches = this.getInputValue('maxTouches') as number;

    const touchPoints = this.inputManager.getTouchPoints();
    const touchCount = touchPoints.length;
    const touching = touchCount > 0;

    // 获取主触摸点位置
    let mainPosition = { x: 0, y: 0 };
    if (touchPoints.length > 0) {
      if (touchIndex >= 0 && touchIndex < touchPoints.length) {
        const touch = touchPoints[touchIndex];
        mainPosition = { x: touch.x, y: touch.y };
      } else {
        // 使用第一个触摸点
        const touch = touchPoints[0];
        mainPosition = { x: touch.x, y: touch.y };
      }
    }

    // 计算平均位置
    let averagePosition = { x: 0, y: 0 };
    if (touchPoints.length > 0) {
      const sum = touchPoints.reduce(
        (acc, touch) => ({ x: acc.x + touch.x, y: acc.y + touch.y }),
        { x: 0, y: 0 }
      );
      averagePosition = {
        x: sum.x / touchPoints.length,
        y: sum.y / touchPoints.length
      };
    }

    // 计算双指间距离
    let pinchDistance = 0;
    let isPinching = false;
    if (touchPoints.length >= 2) {
      const touch1 = touchPoints[0];
      const touch2 = touchPoints[1];
      pinchDistance = Math.sqrt(
        Math.pow(touch2.x - touch1.x, 2) + Math.pow(touch2.y - touch1.y, 2)
      );
      isPinching = true;
    }

    // 限制触摸点数量
    const limitedTouches = touchPoints.slice(0, maxTouches);

    // 设置输出值
    this.setOutputValue('touching', touching);
    this.setOutputValue('touchCount', touchCount);
    this.setOutputValue('position', mainPosition);
    this.setOutputValue('allTouches', limitedTouches);
    this.setOutputValue('averagePosition', averagePosition);
    this.setOutputValue('pinchDistance', pinchDistance);
    this.setOutputValue('isPinching', isPinching);

    return {
      touching,
      touchCount,
      position: mainPosition,
      allTouches: limitedTouches,
      averagePosition,
      pinchDistance,
      isPinching
    };
  }
}

/**
 * 游戏手柄输入节点
 */
export class GamepadInputNode extends FunctionNode {
  private inputManager: InputManager;

  constructor(options: any) {
    super(options);
    this.inputManager = InputManager.getInstance();
    this.inputManager.initialize();
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入插槽
    this.addInput({
      name: 'gamepadIndex',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '游戏手柄索引（0-3）',
      defaultValue: 0
    });

    this.addInput({
      name: 'button',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '按钮索引',
      defaultValue: GamepadButton.A,
      optional: true
    });

    this.addInput({
      name: 'deadzone',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '摇杆死区',
      defaultValue: 0.1,
      optional: true
    });

    // 添加输出插槽
    this.addOutput({
      name: 'connected',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否连接'
    });

    this.addOutput({
      name: 'pressed',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '指定按钮是否按下'
    });

    this.addOutput({
      name: 'leftStick',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '左摇杆'
    });

    this.addOutput({
      name: 'rightStick',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '右摇杆'
    });

    this.addOutput({
      name: 'leftTrigger',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '左扳机'
    });

    this.addOutput({
      name: 'rightTrigger',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '右扳机'
    });

    this.addOutput({
      name: 'dpad',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '方向键'
    });

    this.addOutput({
      name: 'buttons',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '所有按钮状态'
    });
  }

  /**
   * 执行节点
   */
  public execute(): any {
    const gamepadIndex = this.getInputValue('gamepadIndex') as number;
    const buttonIndex = this.getInputValue('button') as number;
    const deadzone = this.getInputValue('deadzone') as number;

    const gamepad = this.inputManager.getGamepad(gamepadIndex);

    if (!gamepad) {
      this.setDisconnectedOutputs();
      return this.getDisconnectedResult();
    }

    // 处理摇杆输入（应用死区）
    const leftStick = this.applyDeadzone(
      { x: gamepad.axes[0] || 0, y: gamepad.axes[1] || 0 },
      deadzone
    );

    const rightStick = this.applyDeadzone(
      { x: gamepad.axes[2] || 0, y: gamepad.axes[3] || 0 },
      deadzone
    );

    // 获取扳机值
    const leftTrigger = gamepad.buttons[GamepadButton.LEFT_TRIGGER]?.value || 0;
    const rightTrigger = gamepad.buttons[GamepadButton.RIGHT_TRIGGER]?.value || 0;

    // 获取方向键状态
    const dpad = {
      up: gamepad.buttons[GamepadButton.DPAD_UP]?.pressed || false,
      down: gamepad.buttons[GamepadButton.DPAD_DOWN]?.pressed || false,
      left: gamepad.buttons[GamepadButton.DPAD_LEFT]?.pressed || false,
      right: gamepad.buttons[GamepadButton.DPAD_RIGHT]?.pressed || false
    };

    // 获取所有按钮状态
    const buttons = Array.from(gamepad.buttons).map((button, index) => ({
      index,
      pressed: button.pressed,
      value: button.value
    }));

    // 获取指定按钮状态
    const buttonPressed = gamepad.buttons[buttonIndex]?.pressed || false;

    // 设置输出值
    this.setOutputValue('connected', true);
    this.setOutputValue('pressed', buttonPressed);
    this.setOutputValue('leftStick', leftStick);
    this.setOutputValue('rightStick', rightStick);
    this.setOutputValue('leftTrigger', leftTrigger);
    this.setOutputValue('rightTrigger', rightTrigger);
    this.setOutputValue('dpad', dpad);
    this.setOutputValue('buttons', buttons);

    return {
      connected: true,
      pressed: buttonPressed,
      leftStick,
      rightStick,
      leftTrigger,
      rightTrigger,
      dpad,
      buttons
    };
  }

  private applyDeadzone(stick: { x: number; y: number }, deadzone: number): { x: number; y: number } {
    const magnitude = Math.sqrt(stick.x * stick.x + stick.y * stick.y);

    if (magnitude < deadzone) {
      return { x: 0, y: 0 };
    }

    // 重新映射到去除死区后的范围
    const normalizedMagnitude = (magnitude - deadzone) / (1 - deadzone);
    const scale = normalizedMagnitude / magnitude;

    return {
      x: stick.x * scale,
      y: stick.y * scale
    };
  }

  private setDisconnectedOutputs(): void {
    this.setOutputValue('connected', false);
    this.setOutputValue('pressed', false);
    this.setOutputValue('leftStick', { x: 0, y: 0 });
    this.setOutputValue('rightStick', { x: 0, y: 0 });
    this.setOutputValue('leftTrigger', 0);
    this.setOutputValue('rightTrigger', 0);
    this.setOutputValue('dpad', { up: false, down: false, left: false, right: false });
    this.setOutputValue('buttons', []);
  }

  private getDisconnectedResult(): any {
    return {
      connected: false,
      pressed: false,
      leftStick: { x: 0, y: 0 },
      rightStick: { x: 0, y: 0 },
      leftTrigger: 0,
      rightTrigger: 0,
      dpad: { up: false, down: false, left: false, right: false },
      buttons: []
    };
  }
}

/**
 * 输入映射节点
 * 将输入映射到自定义动作
 */
export class InputMappingNode extends FunctionNode {
  private actionMappings: Map<string, string[]> = new Map();

  constructor(options: any) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    this.addInput({
      name: 'actionName',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '动作名称',
      defaultValue: 'jump'
    });

    this.addInput({
      name: 'keyMappings',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '键位映射数组',
      defaultValue: ['Space', 'KeyW'],
      optional: true
    });

    this.addOutput({
      name: 'actionPressed',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '动作是否触发'
    });

    this.addOutput({
      name: 'actionJustPressed',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '动作刚触发'
    });

    this.addOutput({
      name: 'triggerKey',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '触发的按键'
    });
  }

  public execute(): any {
    const actionName = this.getInputValue('actionName') as string;
    const keyMappings = this.getInputValue('keyMappings') as string[];

    const inputManager = InputManager.getInstance();

    let actionPressed = false;
    let actionJustPressed = false;
    let triggerKey = '';

    // 检查所有映射的按键
    for (const key of keyMappings) {
      const keyState = inputManager.getKeyState(key);
      if (keyState?.pressed) {
        actionPressed = true;
        triggerKey = key;
      }
      if (keyState?.justPressed) {
        actionJustPressed = true;
        triggerKey = key;
      }
    }

    this.setOutputValue('actionPressed', actionPressed);
    this.setOutputValue('actionJustPressed', actionJustPressed);
    this.setOutputValue('triggerKey', triggerKey);

    return {
      actionPressed,
      actionJustPressed,
      triggerKey
    };
  }
}

/**
 * 输入序列检测节点
 * 检测按键序列（如格斗游戏的连招）
 */
export class InputSequenceNode extends FunctionNode {
  private sequence: string[] = [];
  private inputHistory: Array<{ key: string; time: number }> = [];
  private maxHistoryTime: number = 2000; // 2秒

  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'sequence',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '按键序列',
      defaultValue: ['KeyA', 'KeyS', 'KeyD']
    });

    this.addInput({
      name: 'timeWindow',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '时间窗口（毫秒）',
      defaultValue: 2000,
      optional: true
    });

    this.addOutput({
      name: 'sequenceCompleted',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '序列是否完成'
    });

    this.addOutput({
      name: 'progress',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '序列进度（0-1）'
    });
  }

  public execute(): any {
    const sequence = this.getInputValue('sequence') as string[];
    const timeWindow = this.getInputValue('timeWindow') as number;

    const inputManager = InputManager.getInstance();
    const now = Date.now();

    // 清理过期的输入历史
    this.inputHistory = this.inputHistory.filter(
      input => now - input.time <= timeWindow
    );

    // 检查新的按键输入
    for (const key of sequence) {
      const keyState = inputManager.getKeyState(key);
      if (keyState?.justPressed) {
        this.inputHistory.push({ key, time: now });
        break; // 一次只处理一个按键
      }
    }

    // 检查序列匹配
    const sequenceCompleted = this.checkSequence(sequence);
    const progress = this.calculateProgress(sequence);

    this.setOutputValue('sequenceCompleted', sequenceCompleted);
    this.setOutputValue('progress', progress);

    return {
      sequenceCompleted,
      progress
    };
  }

  private checkSequence(sequence: string[]): boolean {
    if (this.inputHistory.length < sequence.length) {
      return false;
    }

    const recentInputs = this.inputHistory.slice(-sequence.length);
    return sequence.every((key, index) => recentInputs[index].key === key);
  }

  private calculateProgress(sequence: string[]): number {
    if (sequence.length === 0) return 0;

    let matchCount = 0;
    const minLength = Math.min(this.inputHistory.length, sequence.length);

    for (let i = 0; i < minLength; i++) {
      const historyIndex = this.inputHistory.length - minLength + i;
      if (this.inputHistory[historyIndex].key === sequence[i]) {
        matchCount++;
      } else {
        break; // 序列必须连续匹配
      }
    }

    return matchCount / sequence.length;
  }
}

/**
 * 注册输入节点
 */
export function registerInputNodes(registry: NodeRegistry): void {
  // 注册键盘输入节点
  registry.registerNodeType({
    type: 'input/keyboard',
    category: NodeCategory.INPUT,
    constructor: KeyboardInputNode,
    label: '键盘输入',
    description: '获取键盘按键状态',
    icon: 'keyboard',
    color: '#2196F3',
    tags: ['input', 'keyboard', 'key']
  });

  // 注册鼠标输入节点
  registry.registerNodeType({
    type: 'input/mouse',
    category: NodeCategory.INPUT,
    constructor: MouseInputNode,
    label: '鼠标输入',
    description: '获取鼠标按钮和位置信息',
    icon: 'mouse',
    color: '#2196F3',
    tags: ['input', 'mouse', 'click', 'position']
  });

  // 注册触摸输入节点
  registry.registerNodeType({
    type: 'input/touch',
    category: NodeCategory.INPUT,
    constructor: TouchInputNode,
    label: '触摸输入',
    description: '获取触摸屏输入信息',
    icon: 'touch',
    color: '#2196F3',
    tags: ['input', 'touch', 'mobile', 'gesture']
  });

  // 注册游戏手柄输入节点
  registry.registerNodeType({
    type: 'input/gamepad',
    category: NodeCategory.INPUT,
    constructor: GamepadInputNode,
    label: '游戏手柄输入',
    description: '获取游戏手柄输入信息',
    icon: 'gamepad',
    color: '#2196F3',
    tags: ['input', 'gamepad', 'controller', 'joystick']
  });

  // 注册输入映射节点
  registry.registerNodeType({
    type: 'input/mapping',
    category: NodeCategory.INPUT,
    constructor: InputMappingNode,
    label: '输入映射',
    description: '将输入映射到自定义动作',
    icon: 'mapping',
    color: '#4CAF50',
    tags: ['input', 'mapping', 'action', 'binding']
  });

  // 注册输入序列检测节点
  registry.registerNodeType({
    type: 'input/sequence',
    category: NodeCategory.INPUT,
    constructor: InputSequenceNode,
    label: '输入序列',
    description: '检测按键序列输入',
    icon: 'sequence',
    color: '#FF9800',
    tags: ['input', 'sequence', 'combo', 'pattern']
  });
}

/**
 * 获取输入管理器实例
 */
export function getInputManager(): InputManager {
  return InputManager.getInstance();
}
