/**
 * 视觉脚本WebRTC节点
 * 提供WebRTC相关功能，如媒体流控制、数据通道管理等
 */
import { AsyncNode } from '../nodes/AsyncNode';
import { EventNode } from '../nodes/EventNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { NetworkSystem } from '../../network/NetworkSystem';
import { WebRTCConnection } from '../../network/WebRTCConnection';
import { WebRTCDataChannel } from '../../network/WebRTCDataChannel';

/**
 * 创建WebRTC连接节点
 * 创建与远程对等方的WebRTC连接
 */
export class CreateWebRTCConnectionNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'peerId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '对等方ID'
    });

    this.addInput({
      name: 'config',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'WebRTC配置',
      defaultValue: {},
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '创建成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '创建失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'WebRTC连接'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const peerId = this.getInputValue('peerId') as string;

    // 检查输入值是否有效
    if (!peerId) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取网络系统
    const networkSystem = this.context.world.getSystem(NetworkSystem);
    if (!networkSystem) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 创建WebRTC连接
      const connection = networkSystem.createWebRTCConnection(peerId);
      
      if (connection) {
        // 设置输出值
        this.setOutputValue('connection', connection);
        
        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('创建WebRTC连接失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 发送数据通道消息节点
 * 通过WebRTC连接发送数据
 */
export class SendDataChannelMessageNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'WebRTC连接'
    });

    this.addInput({
      name: 'message',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '要发送的消息'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '发送成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '发送失败'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const connection = this.getInputValue('connection') as WebRTCConnection;
    const message = this.getInputValue('message');

    // 检查输入值是否有效
    if (!connection || message === undefined) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 发送消息
      await connection.send(message);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('发送数据通道消息失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 数据通道消息事件节点
 * 监听数据通道消息事件
 */
export class DataChannelMessageEventNode extends EventNode {
  /** 数据通道 */
  private dataChannel: WebRTCDataChannel | null = null;

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入数据插槽
    this.addInput({
      name: 'dataChannel',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '数据通道'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '收到消息'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'message',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '消息数据'
    });
  }

  /**
   * 初始化事件
   */
  public initialize(): void {
    // 获取数据通道
    this.dataChannel = this.getInputValue('dataChannel') as WebRTCDataChannel;
    
    if (this.dataChannel) {
      // 监听消息事件
      this.dataChannel.on('message', this.onMessage.bind(this));
    }
  }

  /**
   * 消息事件处理
   * @param message 消息数据
   */
  private onMessage(message: any): void {
    // 设置输出值
    this.setOutputValue('message', message);
    
    // 触发输出流程
    this.triggerFlow('flow');
  }

  /**
   * 清理事件
   */
  public cleanup(): void {
    if (this.dataChannel) {
      // 移除事件监听
      this.dataChannel.off('message', this.onMessage.bind(this));
      this.dataChannel = null;
    }
  }
}

/**
 * 获取媒体流节点
 * 获取用户媒体流（摄像头、麦克风）
 */
export class GetUserMediaNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'enableVideo',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '启用视频',
      defaultValue: true
    });

    this.addInput({
      name: 'enableAudio',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '启用音频',
      defaultValue: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '获取成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '获取失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'mediaStream',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '媒体流'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const enableVideo = this.getInputValue('enableVideo') as boolean;
    const enableAudio = this.getInputValue('enableAudio') as boolean;

    try {
      // 获取用户媒体流
      const stream = await navigator.mediaDevices.getUserMedia({
        video: enableVideo,
        audio: enableAudio
      });

      // 设置输出值
      this.setOutputValue('mediaStream', stream);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('获取媒体流失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 屏幕共享节点
 * 获取屏幕共享流
 */
export class GetDisplayMediaNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'enableAudio',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '启用音频',
      defaultValue: false
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '获取成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '获取失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'displayStream',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '屏幕共享流'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const enableAudio = this.getInputValue('enableAudio') as boolean;

    try {
      // 获取屏幕共享流
      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: enableAudio
      });

      // 设置输出值
      this.setOutputValue('displayStream', stream);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('获取屏幕共享流失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 连接状态监控节点
 * 监控WebRTC连接状态变化
 */
export class WebRTCConnectionStateNode extends EventNode {
  /** WebRTC连接 */
  private connection: WebRTCConnection | null = null;

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入数据插槽
    this.addInput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'WebRTC连接'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'connected',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '连接成功'
    });

    this.addOutput({
      name: 'disconnected',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '连接断开'
    });

    this.addOutput({
      name: 'failed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '连接失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'state',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '连接状态'
    });
  }

  /**
   * 初始化事件
   */
  public initialize(): void {
    // 获取WebRTC连接
    this.connection = this.getInputValue('connection') as WebRTCConnection;

    if (this.connection) {
      // 监听连接状态事件
      this.connection.on('connected', this.onConnected.bind(this));
      this.connection.on('disconnected', this.onDisconnected.bind(this));
      this.connection.on('error', this.onError.bind(this));
    }
  }

  /**
   * 连接成功事件处理
   */
  private onConnected(): void {
    this.setOutputValue('state', 'connected');
    this.triggerFlow('connected');
  }

  /**
   * 连接断开事件处理
   */
  private onDisconnected(): void {
    this.setOutputValue('state', 'disconnected');
    this.triggerFlow('disconnected');
  }

  /**
   * 连接错误事件处理
   */
  private onError(_error: any): void {
    this.setOutputValue('state', 'failed');
    this.triggerFlow('failed');
  }

  /**
   * 清理事件
   */
  public cleanup(): void {
    if (this.connection) {
      this.connection.off('connected', this.onConnected.bind(this));
      this.connection.off('disconnected', this.onDisconnected.bind(this));
      this.connection.off('error', this.onError.bind(this));
      this.connection = null;
    }
  }
}

/**
 * 添加媒体流节点
 * 向WebRTC连接添加媒体流
 */
export class AddMediaStreamNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'WebRTC连接'
    });

    this.addInput({
      name: 'mediaStream',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '媒体流'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '添加成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '添加失败'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const connection = this.getInputValue('connection') as WebRTCConnection;
    const mediaStream = this.getInputValue('mediaStream') as MediaStream;

    // 检查输入值是否有效
    if (!connection || !mediaStream) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 通过反射访问peerConnection来添加轨道
      const peerConnection = (connection as any).peerConnection;
      if (peerConnection) {
        mediaStream.getTracks().forEach(track => {
          peerConnection.addTrack(track, mediaStream);
        });
      }

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('添加媒体流失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 创建WebRTC提议节点
 * 创建WebRTC连接提议
 */
export class CreateOfferNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'WebRTC连接'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '创建成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '创建失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'offer',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'SDP提议'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const connection = this.getInputValue('connection') as WebRTCConnection;

    // 检查输入值是否有效
    if (!connection) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 创建提议
      await connection.createOffer();

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('创建WebRTC提议失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 处理WebRTC提议节点
 * 处理接收到的WebRTC提议
 */
export class HandleOfferNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'WebRTC连接'
    });

    this.addInput({
      name: 'offer',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'SDP提议'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '处理成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '处理失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'answer',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'SDP应答'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const connection = this.getInputValue('connection') as WebRTCConnection;
    const offer = this.getInputValue('offer') as RTCSessionDescriptionInit;

    // 检查输入值是否有效
    if (!connection || !offer) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 处理提议
      await connection.handleOffer(offer);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('处理WebRTC提议失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 处理WebRTC应答节点
 * 处理接收到的WebRTC应答
 */
export class HandleAnswerNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'WebRTC连接'
    });

    this.addInput({
      name: 'answer',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'SDP应答'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '处理成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '处理失败'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const connection = this.getInputValue('connection') as WebRTCConnection;
    const answer = this.getInputValue('answer') as RTCSessionDescriptionInit;

    // 检查输入值是否有效
    if (!connection || !answer) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 处理应答
      await connection.handleAnswer(answer);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('处理WebRTC应答失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 处理ICE候选节点
 * 处理接收到的ICE候选
 */
export class HandleIceCandidateNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'WebRTC连接'
    });

    this.addInput({
      name: 'candidate',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'ICE候选'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '处理成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '处理失败'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const connection = this.getInputValue('connection') as WebRTCConnection;
    const candidate = this.getInputValue('candidate') as RTCIceCandidateInit;

    // 检查输入值是否有效
    if (!connection || !candidate) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 处理ICE候选
      await connection.handleIceCandidate(candidate);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('处理ICE候选失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 远程媒体流事件节点
 * 监听远程媒体流事件
 */
export class RemoteStreamEventNode extends EventNode {
  /** WebRTC连接 */
  private connection: WebRTCConnection | null = null;

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入数据插槽
    this.addInput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'WebRTC连接'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '收到远程流'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'remoteStream',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '远程媒体流'
    });
  }

  /**
   * 初始化事件
   */
  public initialize(): void {
    // 获取WebRTC连接
    this.connection = this.getInputValue('connection') as WebRTCConnection;

    if (this.connection) {
      // 监听远程流事件
      this.connection.on('track', this.onRemoteStream.bind(this));
    }
  }

  /**
   * 远程流事件处理
   * @param _track 媒体轨道
   * @param stream 媒体流
   */
  private onRemoteStream(_track: MediaStreamTrack, stream: MediaStream): void {
    // 设置输出值
    this.setOutputValue('remoteStream', stream);

    // 触发输出流程
    this.triggerFlow('flow');
  }

  /**
   * 清理事件
   */
  public cleanup(): void {
    if (this.connection) {
      this.connection.off('track', this.onRemoteStream.bind(this));
      this.connection = null;
    }
  }
}

/**
 * 断开WebRTC连接节点
 * 断开WebRTC连接
 */
export class DisconnectWebRTCNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'WebRTC连接'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '断开成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '断开失败'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const connection = this.getInputValue('connection') as WebRTCConnection;

    // 检查输入值是否有效
    if (!connection) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 断开连接
      await connection.disconnect();

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('断开WebRTC连接失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 注册WebRTC节点
 * @param registry 节点注册表
 */
export function registerWebRTCNodes(registry: NodeRegistry): void {
  // 注册创建WebRTC连接节点
  registry.registerNodeType({
    type: 'network/webrtc/createConnection',
    category: NodeCategory.NETWORK,
    constructor: CreateWebRTCConnectionNode,
    label: '创建WebRTC连接',
    description: '创建与远程对等方的WebRTC连接',
    icon: 'webrtc',
    color: '#00BCD4',
    tags: ['network', 'webrtc', 'connection']
  });

  // 注册发送数据通道消息节点
  registry.registerNodeType({
    type: 'network/webrtc/sendDataChannelMessage',
    category: NodeCategory.NETWORK,
    constructor: SendDataChannelMessageNode,
    label: '发送数据通道消息',
    description: '通过WebRTC连接发送数据',
    icon: 'send',
    color: '#00BCD4',
    tags: ['network', 'webrtc', 'datachannel', 'send']
  });

  // 注册数据通道消息事件节点
  registry.registerNodeType({
    type: 'network/webrtc/onDataChannelMessage',
    category: NodeCategory.NETWORK,
    constructor: DataChannelMessageEventNode,
    label: '数据通道消息事件',
    description: '监听数据通道消息事件',
    icon: 'message',
    color: '#00BCD4',
    tags: ['network', 'webrtc', 'datachannel', 'message', 'event']
  });

  // 注册获取用户媒体节点
  registry.registerNodeType({
    type: 'network/webrtc/getUserMedia',
    category: NodeCategory.NETWORK,
    constructor: GetUserMediaNode,
    label: '获取用户媒体',
    description: '获取用户媒体流（摄像头、麦克风）',
    icon: 'camera',
    color: '#00BCD4',
    tags: ['network', 'webrtc', 'media', 'camera', 'microphone']
  });

  // 注册获取屏幕共享节点
  registry.registerNodeType({
    type: 'network/webrtc/getDisplayMedia',
    category: NodeCategory.NETWORK,
    constructor: GetDisplayMediaNode,
    label: '获取屏幕共享',
    description: '获取屏幕共享流',
    icon: 'screen',
    color: '#00BCD4',
    tags: ['network', 'webrtc', 'screen', 'share']
  });

  // 注册连接状态监控节点
  registry.registerNodeType({
    type: 'network/webrtc/connectionState',
    category: NodeCategory.NETWORK,
    constructor: WebRTCConnectionStateNode,
    label: 'WebRTC连接状态',
    description: '监控WebRTC连接状态变化',
    icon: 'status',
    color: '#00BCD4',
    tags: ['network', 'webrtc', 'connection', 'state', 'monitor']
  });

  // 注册添加媒体流节点
  registry.registerNodeType({
    type: 'network/webrtc/addMediaStream',
    category: NodeCategory.NETWORK,
    constructor: AddMediaStreamNode,
    label: '添加媒体流',
    description: '向WebRTC连接添加媒体流',
    icon: 'add-stream',
    color: '#00BCD4',
    tags: ['network', 'webrtc', 'media', 'stream', 'add']
  });

  // 注册创建提议节点
  registry.registerNodeType({
    type: 'network/webrtc/createOffer',
    category: NodeCategory.NETWORK,
    constructor: CreateOfferNode,
    label: '创建WebRTC提议',
    description: '创建WebRTC连接提议',
    icon: 'offer',
    color: '#00BCD4',
    tags: ['network', 'webrtc', 'offer', 'sdp']
  });

  // 注册处理提议节点
  registry.registerNodeType({
    type: 'network/webrtc/handleOffer',
    category: NodeCategory.NETWORK,
    constructor: HandleOfferNode,
    label: '处理WebRTC提议',
    description: '处理接收到的WebRTC提议',
    icon: 'handle-offer',
    color: '#00BCD4',
    tags: ['network', 'webrtc', 'offer', 'handle', 'sdp']
  });

  // 注册处理应答节点
  registry.registerNodeType({
    type: 'network/webrtc/handleAnswer',
    category: NodeCategory.NETWORK,
    constructor: HandleAnswerNode,
    label: '处理WebRTC应答',
    description: '处理接收到的WebRTC应答',
    icon: 'handle-answer',
    color: '#00BCD4',
    tags: ['network', 'webrtc', 'answer', 'handle', 'sdp']
  });

  // 注册处理ICE候选节点
  registry.registerNodeType({
    type: 'network/webrtc/handleIceCandidate',
    category: NodeCategory.NETWORK,
    constructor: HandleIceCandidateNode,
    label: '处理ICE候选',
    description: '处理接收到的ICE候选',
    icon: 'ice-candidate',
    color: '#00BCD4',
    tags: ['network', 'webrtc', 'ice', 'candidate']
  });

  // 注册远程媒体流事件节点
  registry.registerNodeType({
    type: 'network/webrtc/onRemoteStream',
    category: NodeCategory.NETWORK,
    constructor: RemoteStreamEventNode,
    label: '远程媒体流事件',
    description: '监听远程媒体流事件',
    icon: 'remote-stream',
    color: '#00BCD4',
    tags: ['network', 'webrtc', 'remote', 'stream', 'event']
  });

  // 注册断开连接节点
  registry.registerNodeType({
    type: 'network/webrtc/disconnect',
    category: NodeCategory.NETWORK,
    constructor: DisconnectWebRTCNode,
    label: '断开WebRTC连接',
    description: '断开WebRTC连接',
    icon: 'disconnect',
    color: '#00BCD4',
    tags: ['network', 'webrtc', 'disconnect']
  });
}
