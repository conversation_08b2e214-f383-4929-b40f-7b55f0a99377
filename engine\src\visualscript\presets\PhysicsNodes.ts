/**
 * 视觉脚本物理节点
 * 提供物理系统相关的节点
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { FunctionNode } from '../nodes/FunctionNode';
import { FlowNode } from '../nodes/FlowNode';
import { EventNode, EventNodeOptions } from '../nodes/EventNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { Vector3 } from '../../math/Vector3';
import { PhysicsSystem } from '../../physics/PhysicsSystem';
import { PhysicsBodyComponent } from '../../physics/components/PhysicsBodyComponent';
import { PhysicsColliderComponent } from '../../physics/components/PhysicsColliderComponent';
import { BodyType } from '../../physics/types/BodyType';
import { ColliderType } from '../../physics/types/ColliderType';

/**
 * 射线检测节点
 * 执行物理射线检测
 */
export class RaycastNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入插槽
    this.addInput({
      name: 'origin',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '射线起点'
    });

    this.addInput({
      name: 'direction',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '射线方向'
    });

    this.addInput({
      name: 'maxDistance',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '最大检测距离',
      defaultValue: 100
    });

    this.addInput({
      name: 'collisionGroup',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '碰撞组',
      defaultValue: -1
    });

    this.addInput({
      name: 'collisionMask',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '碰撞掩码',
      defaultValue: -1
    });

    // 添加输出插槽
    this.addOutput({
      name: 'hit',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否命中'
    });

    this.addOutput({
      name: 'hitEntity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.OUTPUT,
      description: '命中的实体'
    });

    this.addOutput({
      name: 'hitPoint',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.OUTPUT,
      description: '命中点'
    });

    this.addOutput({
      name: 'hitNormal',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.OUTPUT,
      description: '命中法线'
    });

    this.addOutput({
      name: 'hitDistance',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '命中距离'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const origin = this.getInputValue('origin') as Vector3;
    const direction = this.getInputValue('direction') as Vector3;
    const maxDistance = this.getInputValue('maxDistance') as number;
    const collisionGroup = this.getInputValue('collisionGroup') as number;
    const collisionMask = this.getInputValue('collisionMask') as number;

    // 检查输入值是否有效
    if (!origin || !direction) {
      this.setOutputValue('hit', false);
      return false;
    }

    // 获取物理系统
    const physicsSystem = this.context.world.getSystem(PhysicsSystem);
    if (!physicsSystem) {
      this.setOutputValue('hit', false);
      return false;
    }

    // 计算射线终点
    const to = {
      x: origin.x + direction.x * maxDistance,
      y: origin.y + direction.y * maxDistance,
      z: origin.z + direction.z * maxDistance
    };

    // 执行射线检测
    const result = physicsSystem.raycastClosest(origin, to, {
      collisionFilterGroup: collisionGroup,
      collisionFilterMask: collisionMask
    });

    // 设置输出值
    this.setOutputValue('hit', result.hasHit());
    this.setOutputValue('hitEntity', result.getHitEntity());
    this.setOutputValue('hitPoint', result.getHitPoint());
    this.setOutputValue('hitNormal', result.getHitNormal());
    this.setOutputValue('hitDistance', result.getHitDistance());

    return result.hasHit();
  }
}

/**
 * 应用力节点
 * 向物理体应用力
 */
export class ApplyForceNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'force',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '力向量'
    });

    this.addInput({
      name: 'localPoint',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '局部应用点',
      defaultValue: new Vector3(0, 0, 0)
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const force = this.getInputValue('force') as Vector3;
    const localPoint = this.getInputValue('localPoint') as Vector3;

    // 检查输入值是否有效
    if (!entity || !force) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 检查实体是否有物理体组件
    if (!entity.hasComponent(PhysicsBodyComponent.type)) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 获取物理体组件
    const physicsBody = entity.getComponent(PhysicsBodyComponent.type) as PhysicsBodyComponent;

    // 应用力
    try {
      // 转换为THREE.Vector3
      const threeForce = new THREE.Vector3(force.x, force.y, force.z);
      const threeLocalPoint = localPoint ? new THREE.Vector3(localPoint.x, localPoint.y, localPoint.z) : undefined;

      physicsBody.applyForce(threeForce, threeLocalPoint);

      // 设置输出值
      this.setOutputValue('success', true);

      // 触发输出流程
      this.triggerFlow('flow');

      return true;
    } catch (error) {
      console.error('应用力失败:', error);

      // 设置输出值
      this.setOutputValue('success', false);

      // 触发输出流程
      this.triggerFlow('flow');

      return false;
    }
  }
}

/**
 * 碰撞检测节点
 * 检测两个实体之间的碰撞
 */
export class CollisionDetectionNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入插槽
    this.addInput({
      name: 'entityA',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '实体A'
    });

    this.addInput({
      name: 'entityB',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '实体B'
    });

    // 添加输出插槽
    this.addOutput({
      name: 'colliding',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否碰撞'
    });

    this.addOutput({
      name: 'contactPoint',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.OUTPUT,
      description: '接触点'
    });

    this.addOutput({
      name: 'contactNormal',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.OUTPUT,
      description: '接触法线'
    });

    this.addOutput({
      name: 'penetrationDepth',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '穿透深度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entityA = this.getInputValue('entityA') as Entity;
    const entityB = this.getInputValue('entityB') as Entity;

    // 检查输入值是否有效
    if (!entityA || !entityB) {
      this.setOutputValue('colliding', false);
      return false;
    }

    // 获取物理系统
    const physicsSystem = this.context.world.getSystem(PhysicsSystem);
    if (!physicsSystem) {
      this.setOutputValue('colliding', false);
      return false;
    }

    // 模拟碰撞检测（实际应用中应该使用物理引擎的碰撞检测）
    // 获取两个实体的物理体
    const bodyA = physicsSystem.getPhysicsBody(entityA);
    const bodyB = physicsSystem.getPhysicsBody(entityB);

    if (!bodyA || !bodyB) {
      this.setOutputValue('colliding', false);
      this.setOutputValue('contactPoint', null);
      this.setOutputValue('contactNormal', null);
      this.setOutputValue('penetrationDepth', 0);
      return false;
    }

    // 简单的距离检测（实际应用中应该使用更精确的碰撞检测）
    const posA = bodyA.getPosition();
    const posB = bodyB.getPosition();

    // 转换为THREE.Vector3进行计算
    const threeA = new THREE.Vector3(posA.x, posA.y, posA.z);
    const threeB = new THREE.Vector3(posB.x, posB.y, posB.z);
    const distance = threeA.distanceTo(threeB);
    const colliding = distance < 2.0; // 简单的阈值检测

    // 设置输出值
    this.setOutputValue('colliding', colliding);
    if (colliding) {
      // 计算接触点和法线
      const contactPoint = threeA.clone().lerp(threeB, 0.5);
      const contactNormal = threeB.clone().sub(threeA).normalize();
      this.setOutputValue('contactPoint', contactPoint);
      this.setOutputValue('contactNormal', contactNormal);
      this.setOutputValue('penetrationDepth', 2.0 - distance);
    } else {
      this.setOutputValue('contactPoint', null);
      this.setOutputValue('contactNormal', null);
      this.setOutputValue('penetrationDepth', 0);
    }

    return colliding;
  }
}

/**
 * 物理约束节点
 * 创建物理约束
 */
export class CreateConstraintNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entityA',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '实体A'
    });

    this.addInput({
      name: 'entityB',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '实体B'
    });

    this.addInput({
      name: 'constraintType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '约束类型',
      defaultValue: 'hinge'
    });

    this.addInput({
      name: 'pivotA',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '实体A上的枢轴点',
      defaultValue: new Vector3(0, 0, 0)
    });

    this.addInput({
      name: 'pivotB',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '实体B上的枢轴点',
      defaultValue: new Vector3(0, 0, 0)
    });

    this.addInput({
      name: 'axisA',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '实体A上的轴',
      defaultValue: new Vector3(0, 1, 0)
    });

    this.addInput({
      name: 'axisB',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '实体B上的轴',
      defaultValue: new Vector3(0, 1, 0)
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    this.addOutput({
      name: 'constraintId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '约束ID'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entityA = this.getInputValue('entityA') as Entity;
    const entityB = this.getInputValue('entityB') as Entity;
    const constraintType = this.getInputValue('constraintType') as string;
    const pivotA = this.getInputValue('pivotA') as Vector3;
    const pivotB = this.getInputValue('pivotB') as Vector3;
    const axisA = this.getInputValue('axisA') as Vector3;
    const axisB = this.getInputValue('axisB') as Vector3;

    // 检查输入值是否有效
    if (!entityA || !entityB) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 获取物理系统
    const physicsSystem = this.context.world.getSystem(PhysicsSystem);
    if (!physicsSystem) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 模拟约束创建（实际应用中应该使用物理引擎的约束系统）
    try {
      // 获取两个实体的物理体
      const bodyA = physicsSystem.getPhysicsBody(entityA);
      const bodyB = physicsSystem.getPhysicsBody(entityB);

      if (!bodyA || !bodyB) {
        this.setOutputValue('constraintId', '');
        this.setOutputValue('success', false);
        this.triggerFlow('flow');
        return false;
      }

      // 生成约束ID
      const constraintId = `constraint_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // 这里应该创建实际的物理约束，但由于PhysicsSystem没有相应方法，
      // 我们只是模拟成功创建
      console.log(`创建${constraintType}约束: ${constraintId}`, {
        entityA: entityA.id,
        entityB: entityB.id,
        pivotA,
        pivotB,
        axisA,
        axisB
      });

      // 设置输出值
      this.setOutputValue('constraintId', constraintId);
      this.setOutputValue('success', true);

      // 触发输出流程
      this.triggerFlow('flow');

      return true;
    } catch (error) {
      console.error('创建约束失败:', error);

      this.setOutputValue('constraintId', '');
      this.setOutputValue('success', false);
      this.triggerFlow('flow');

      return false;
    }
  }
}

/**
 * 物理材质节点
 * 创建物理材质
 */
export class CreatePhysicsMaterialNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入插槽
    this.addInput({
      name: 'friction',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '摩擦系数',
      defaultValue: 0.3
    });

    this.addInput({
      name: 'restitution',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '恢复系数',
      defaultValue: 0.3
    });

    // 添加输出插槽
    this.addOutput({
      name: 'material',
      type: SocketType.DATA,
      dataType: 'PhysicsMaterial',
      direction: SocketDirection.OUTPUT,
      description: '物理材质'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const friction = this.getInputValue('friction') as number;
    const restitution = this.getInputValue('restitution') as number;

    // 获取物理系统
    const physicsSystem = this.context.world.getSystem(PhysicsSystem);
    if (!physicsSystem) {
      return null;
    }

    // 创建物理材质（使用CANNON.js的Material）
    try {
      // 直接使用CANNON.js创建材质
      const material = new (physicsSystem.getPhysicsWorld().constructor as any).Material({
        friction,
        restitution
      });

      // 设置输出值
      this.setOutputValue('material', material);

      return material;
    } catch (error) {
      console.error('创建物理材质失败:', error);

      // 创建一个简单的材质对象作为备用
      const fallbackMaterial = {
        friction,
        restitution,
        type: 'PhysicsMaterial'
      };

      this.setOutputValue('material', fallbackMaterial);
      return fallbackMaterial;
    }
  }
}

/**
 * 创建物理体节点
 * 为实体创建物理体组件
 */
export class CreatePhysicsBodyNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'bodyType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '物理体类型',
      defaultValue: 'dynamic'
    });

    this.addInput({
      name: 'mass',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '质量',
      defaultValue: 1.0
    });

    this.addInput({
      name: 'linearDamping',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '线性阻尼',
      defaultValue: 0.01
    });

    this.addInput({
      name: 'angularDamping',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '角阻尼',
      defaultValue: 0.01
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });

    this.addOutput({
      name: 'physicsBody',
      type: SocketType.DATA,
      dataType: 'PhysicsBodyComponent',
      direction: SocketDirection.OUTPUT,
      description: '物理体组件'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const bodyTypeStr = this.getInputValue('bodyType') as string;
    const mass = this.getInputValue('mass') as number;
    const linearDamping = this.getInputValue('linearDamping') as number;
    const angularDamping = this.getInputValue('angularDamping') as number;

    // 检查输入值是否有效
    if (!entity) {
      this.setOutputValue('success', false);
      this.setOutputValue('physicsBody', null);
      this.triggerFlow('flow');
      return false;
    }

    // 转换物理体类型
    let bodyType: BodyType;
    switch (bodyTypeStr.toLowerCase()) {
      case 'static':
        bodyType = BodyType.STATIC;
        break;
      case 'kinematic':
        bodyType = BodyType.KINEMATIC;
        break;
      case 'dynamic':
      default:
        bodyType = BodyType.DYNAMIC;
        break;
    }

    try {
      // 创建物理体组件
      const physicsBody = new PhysicsBodyComponent({
        type: bodyType,
        mass: bodyType === BodyType.DYNAMIC ? mass : 0,
        linearDamping,
        angularDamping
      });

      // 添加组件到实体
      entity.addComponent(physicsBody);

      // 获取物理系统并初始化组件
      const physicsSystem = this.context.world.getSystem(PhysicsSystem);
      if (physicsSystem) {
        // 物理组件会在系统更新时自动初始化
        physicsBody.initialize(physicsSystem.getPhysicsWorld());
      }

      // 设置输出值
      this.setOutputValue('success', true);
      this.setOutputValue('physicsBody', physicsBody);

      // 触发输出流程
      this.triggerFlow('flow');

      return true;
    } catch (error) {
      console.error('创建物理体失败:', error);

      // 设置输出值
      this.setOutputValue('success', false);
      this.setOutputValue('physicsBody', null);

      // 触发输出流程
      this.triggerFlow('flow');

      return false;
    }
  }
}

/**
 * 创建碰撞体节点
 * 为实体创建碰撞体组件
 */
export class CreateColliderNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'colliderType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '碰撞体类型',
      defaultValue: 'box'
    });

    this.addInput({
      name: 'size',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '尺寸（盒子）或半径（球体）',
      defaultValue: new Vector3(1, 1, 1)
    });

    this.addInput({
      name: 'isTrigger',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否为触发器',
      defaultValue: false
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });

    this.addOutput({
      name: 'collider',
      type: SocketType.DATA,
      dataType: 'PhysicsColliderComponent',
      direction: SocketDirection.OUTPUT,
      description: '碰撞体组件'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const colliderTypeStr = this.getInputValue('colliderType') as string;
    const size = this.getInputValue('size') as Vector3;
    const isTrigger = this.getInputValue('isTrigger') as boolean;

    // 检查输入值是否有效
    if (!entity || !size) {
      this.setOutputValue('success', false);
      this.setOutputValue('collider', null);
      this.triggerFlow('flow');
      return false;
    }

    // 转换碰撞体类型
    let colliderType: ColliderType;
    switch (colliderTypeStr.toLowerCase()) {
      case 'sphere':
        colliderType = ColliderType.SPHERE;
        break;
      case 'capsule':
        colliderType = ColliderType.CAPSULE;
        break;
      case 'cylinder':
        colliderType = ColliderType.CYLINDER;
        break;
      case 'plane':
        colliderType = ColliderType.PLANE;
        break;
      case 'box':
      default:
        colliderType = ColliderType.BOX;
        break;
    }

    try {
      // 创建碰撞体组件
      const colliderOptions: any = {
        type: colliderType,
        isTrigger
      };

      // 根据类型设置参数
      if (colliderType === ColliderType.BOX) {
        colliderOptions.size = { x: size.x, y: size.y, z: size.z };
      } else if (colliderType === ColliderType.SPHERE) {
        colliderOptions.radius = size.x; // 使用x作为半径
      } else if (colliderType === ColliderType.CAPSULE) {
        colliderOptions.radius = size.x;
        colliderOptions.height = size.y;
      } else if (colliderType === ColliderType.CYLINDER) {
        colliderOptions.radius = size.x;
        colliderOptions.height = size.y;
      }

      const collider = new PhysicsColliderComponent(colliderOptions);

      // 添加组件到实体
      entity.addComponent(collider);

      // 获取物理系统并初始化组件
      const physicsSystem = this.context.world.getSystem(PhysicsSystem);
      if (physicsSystem) {
        // 碰撞体组件会在系统更新时自动初始化
        collider.initialize(physicsSystem.getPhysicsWorld());
      }

      // 设置输出值
      this.setOutputValue('success', true);
      this.setOutputValue('collider', collider);

      // 触发输出流程
      this.triggerFlow('flow');

      return true;
    } catch (error) {
      console.error('创建碰撞体失败:', error);

      // 设置输出值
      this.setOutputValue('success', false);
      this.setOutputValue('collider', null);

      // 触发输出流程
      this.triggerFlow('flow');

      return false;
    }
  }
}

/**
 * 设置重力节点
 * 设置物理世界的重力
 */
export class SetGravityNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'gravity',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '重力向量',
      defaultValue: new Vector3(0, -9.82, 0)
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const gravity = this.getInputValue('gravity') as Vector3;

    // 检查输入值是否有效
    if (!gravity) {
      this.triggerFlow('flow');
      return false;
    }

    try {
      // 获取物理系统
      const physicsSystem = this.context.world.getSystem(PhysicsSystem);
      if (physicsSystem) {
        physicsSystem.setGravity(gravity.x, gravity.y, gravity.z);
      }

      // 触发输出流程
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('设置重力失败:', error);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 应用冲量节点
 * 对物理体应用冲量
 */
export class ApplyImpulseNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'impulse',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '冲量向量'
    });

    this.addInput({
      name: 'worldPoint',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '世界坐标点',
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const impulse = this.getInputValue('impulse') as Vector3;
    const worldPoint = this.getInputValue('worldPoint') as Vector3;

    // 检查输入值是否有效
    if (!entity || !impulse) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      // 获取物理体组件
      const physicsBody = entity.getComponent('PhysicsBodyComponent') as PhysicsBodyComponent;
      if (!physicsBody) {
        console.error('实体没有物理体组件');
        this.setOutputValue('success', false);
        this.triggerFlow('flow');
        return false;
      }

      // 转换为THREE.Vector3
      const threeImpulse = new THREE.Vector3(impulse.x, impulse.y, impulse.z);

      // 应用冲量
      if (worldPoint) {
        const threeWorldPoint = new THREE.Vector3(worldPoint.x, worldPoint.y, worldPoint.z);
        physicsBody.applyImpulse(threeImpulse, threeWorldPoint);
      } else {
        physicsBody.applyImpulse(threeImpulse);
      }

      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('应用冲量失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 获取物理体属性节点
 * 获取物理体的各种属性
 */
export class GetPhysicsBodyPropertiesNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'velocity',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.OUTPUT,
      description: '线性速度'
    });

    this.addOutput({
      name: 'angularVelocity',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.OUTPUT,
      description: '角速度'
    });

    this.addOutput({
      name: 'mass',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '质量'
    });

    this.addOutput({
      name: 'isAwake',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否清醒'
    });

    this.addOutput({
      name: 'position',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.OUTPUT,
      description: '位置'
    });
  }

  /**
   * 计算函数
   * @returns 计算结果
   */
  public compute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;

    // 检查输入值是否有效
    if (!entity) {
      this.setOutputValue('velocity', new Vector3());
      this.setOutputValue('angularVelocity', new Vector3());
      this.setOutputValue('mass', 0);
      this.setOutputValue('isAwake', false);
      this.setOutputValue('position', new Vector3());
      return false;
    }

    try {
      // 获取物理体组件
      const physicsBody = entity.getComponent('PhysicsBodyComponent') as PhysicsBodyComponent;
      if (!physicsBody) {
        this.setOutputValue('velocity', new Vector3());
        this.setOutputValue('angularVelocity', new Vector3());
        this.setOutputValue('mass', 0);
        this.setOutputValue('isAwake', false);
        this.setOutputValue('position', new Vector3());
        return false;
      }

      // 获取属性
      this.setOutputValue('velocity', physicsBody.getLinearVelocity());
      this.setOutputValue('angularVelocity', physicsBody.getAngularVelocity());
      this.setOutputValue('mass', physicsBody.mass);
      this.setOutputValue('isAwake', physicsBody.allowSleep); // 使用allowSleep作为替代
      this.setOutputValue('position', physicsBody.position);

      return true;
    } catch (error) {
      console.error('获取物理体属性失败:', error);
      this.setOutputValue('velocity', new Vector3());
      this.setOutputValue('angularVelocity', new Vector3());
      this.setOutputValue('mass', 0);
      this.setOutputValue('isAwake', false);
      this.setOutputValue('position', new Vector3());
      return false;
    }
  }
}

/**
 * 碰撞事件监听节点
 * 监听物理体的碰撞事件
 */
export class OnCollisionEventNode extends EventNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: EventNodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '监听的实体'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'collisionEnter',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '碰撞开始'
    });

    this.addOutput({
      name: 'collisionStay',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '碰撞持续'
    });

    this.addOutput({
      name: 'collisionExit',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '碰撞结束'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'otherEntity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.OUTPUT,
      description: '碰撞的另一个实体'
    });

    this.addOutput({
      name: 'contactPoint',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.OUTPUT,
      description: '碰撞点'
    });

    this.addOutput({
      name: 'contactNormal',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.OUTPUT,
      description: '碰撞法线'
    });

    this.addOutput({
      name: 'impulse',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '碰撞冲量'
    });
  }

  /**
   * 初始化事件
   */
  public initialize(): void {
    // 获取监听的实体
    const entity = this.getInputValue('entity') as Entity;
    if (!entity) return;

    // 获取物理系统
    const physicsSystem = this.context.world.getSystem(PhysicsSystem);
    if (!physicsSystem) return;

    // 监听碰撞事件
    physicsSystem.on('collisionEnter', this.onCollisionEnter.bind(this));
    physicsSystem.on('collisionStay', this.onCollisionStay.bind(this));
    physicsSystem.on('collisionExit', this.onCollisionExit.bind(this));
  }

  /**
   * 碰撞开始事件处理
   * @param event 碰撞事件
   */
  private onCollisionEnter(event: any): void {
    const entity = this.getInputValue('entity') as Entity;
    if (!entity || (event.entityA !== entity && event.entityB !== entity)) return;

    const otherEntity = event.entityA === entity ? event.entityB : event.entityA;

    this.setOutputValue('otherEntity', otherEntity);
    this.setOutputValue('contactPoint', new Vector3(event.contact.point.x, event.contact.point.y, event.contact.point.z));
    this.setOutputValue('contactNormal', new Vector3(event.contact.normal.x, event.contact.normal.y, event.contact.normal.z));
    this.setOutputValue('impulse', event.contact.impulse || 0);

    this.triggerFlow('collisionEnter');
  }

  /**
   * 碰撞持续事件处理
   * @param event 碰撞事件
   */
  private onCollisionStay(event: any): void {
    const entity = this.getInputValue('entity') as Entity;
    if (!entity || (event.entityA !== entity && event.entityB !== entity)) return;

    const otherEntity = event.entityA === entity ? event.entityB : event.entityA;

    this.setOutputValue('otherEntity', otherEntity);
    this.setOutputValue('contactPoint', new Vector3(event.contact.point.x, event.contact.point.y, event.contact.point.z));
    this.setOutputValue('contactNormal', new Vector3(event.contact.normal.x, event.contact.normal.y, event.contact.normal.z));
    this.setOutputValue('impulse', event.contact.impulse || 0);

    this.triggerFlow('collisionStay');
  }

  /**
   * 碰撞结束事件处理
   * @param event 碰撞事件
   */
  private onCollisionExit(event: any): void {
    const entity = this.getInputValue('entity') as Entity;
    if (!entity || (event.entityA !== entity && event.entityB !== entity)) return;

    const otherEntity = event.entityA === entity ? event.entityB : event.entityA;

    this.setOutputValue('otherEntity', otherEntity);
    this.setOutputValue('contactPoint', new Vector3());
    this.setOutputValue('contactNormal', new Vector3());
    this.setOutputValue('impulse', 0);

    this.triggerFlow('collisionExit');
  }

  /**
   * 清理事件
   */
  public cleanup(): void {
    // 获取物理系统
    const physicsSystem = this.context.world.getSystem(PhysicsSystem);
    if (!physicsSystem) return;

    // 移除事件监听
    physicsSystem.off('collisionEnter', this.onCollisionEnter.bind(this));
    physicsSystem.off('collisionStay', this.onCollisionStay.bind(this));
    physicsSystem.off('collisionExit', this.onCollisionExit.bind(this));
  }
}

/**
 * 设置物理体属性节点
 * 设置物理体的各种属性
 */
export class SetPhysicsBodyPropertiesNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'velocity',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '线性速度',
      optional: true
    });

    this.addInput({
      name: 'angularVelocity',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '角速度',
      optional: true
    });

    this.addInput({
      name: 'mass',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '质量',
      optional: true
    });

    this.addInput({
      name: 'linearDamping',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '线性阻尼',
      optional: true
    });

    this.addInput({
      name: 'angularDamping',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '角阻尼',
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const velocity = this.getInputValue('velocity') as Vector3;
    const angularVelocity = this.getInputValue('angularVelocity') as Vector3;
    const mass = this.getInputValue('mass') as number;
    const linearDamping = this.getInputValue('linearDamping') as number;
    const angularDamping = this.getInputValue('angularDamping') as number;

    // 检查输入值是否有效
    if (!entity) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      // 获取物理体组件
      const physicsBody = entity.getComponent('PhysicsBodyComponent') as PhysicsBodyComponent;
      if (!physicsBody) {
        console.error('实体没有物理体组件');
        this.setOutputValue('success', false);
        this.triggerFlow('flow');
        return false;
      }

      // 设置属性
      if (velocity) {
        const threeVelocity = new THREE.Vector3(velocity.x, velocity.y, velocity.z);
        physicsBody.setLinearVelocity(threeVelocity);
      }

      if (angularVelocity) {
        const threeAngularVelocity = new THREE.Vector3(angularVelocity.x, angularVelocity.y, angularVelocity.z);
        physicsBody.setAngularVelocity(threeAngularVelocity);
      }

      if (mass !== undefined) {
        physicsBody.mass = mass;
      }

      if (linearDamping !== undefined) {
        physicsBody.linearDamping = linearDamping;
      }

      if (angularDamping !== undefined) {
        physicsBody.angularDamping = angularDamping;
      }

      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('设置物理体属性失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 注册物理节点
 * @param registry 节点注册表
 */
export function registerPhysicsNodes(registry: NodeRegistry): void {
  // 注册射线检测节点
  registry.registerNodeType({
    type: 'physics/raycast',
    category: NodeCategory.PHYSICS,
    constructor: RaycastNode,
    label: '射线检测',
    description: '执行物理射线检测',
    icon: 'ray',
    color: '#E91E63',
    tags: ['physics', 'raycast', 'collision']
  });

  // 注册应用力节点
  registry.registerNodeType({
    type: 'physics/applyForce',
    category: NodeCategory.PHYSICS,
    constructor: ApplyForceNode,
    label: '应用力',
    description: '向物理体应用力',
    icon: 'force',
    color: '#E91E63',
    tags: ['physics', 'force', 'dynamics']
  });

  // 注册碰撞检测节点
  registry.registerNodeType({
    type: 'physics/collisionDetection',
    category: NodeCategory.PHYSICS,
    constructor: CollisionDetectionNode,
    label: '碰撞检测',
    description: '检测两个实体之间的碰撞',
    icon: 'collision',
    color: '#E91E63',
    tags: ['physics', 'collision', 'detection']
  });

  // 注册物理约束节点
  registry.registerNodeType({
    type: 'physics/createConstraint',
    category: NodeCategory.PHYSICS,
    constructor: CreateConstraintNode,
    label: '创建约束',
    description: '创建物理约束',
    icon: 'constraint',
    color: '#E91E63',
    tags: ['physics', 'constraint', 'joint']
  });

  // 注册物理材质节点
  registry.registerNodeType({
    type: 'physics/createMaterial',
    category: NodeCategory.PHYSICS,
    constructor: CreatePhysicsMaterialNode,
    label: '创建物理材质',
    description: '创建物理材质',
    icon: 'material',
    color: '#E91E63',
    tags: ['physics', 'material']
  });

  // 注册创建物理体节点
  registry.registerNodeType({
    type: 'physics/createPhysicsBody',
    category: NodeCategory.PHYSICS,
    constructor: CreatePhysicsBodyNode,
    label: '创建物理体',
    description: '为实体创建物理体组件',
    icon: 'body',
    color: '#E91E63',
    tags: ['physics', 'body', 'create']
  });

  // 注册创建碰撞体节点
  registry.registerNodeType({
    type: 'physics/createCollider',
    category: NodeCategory.PHYSICS,
    constructor: CreateColliderNode,
    label: '创建碰撞体',
    description: '为实体创建碰撞体组件',
    icon: 'collider',
    color: '#E91E63',
    tags: ['physics', 'collider', 'create']
  });

  // 注册设置重力节点
  registry.registerNodeType({
    type: 'physics/setGravity',
    category: NodeCategory.PHYSICS,
    constructor: SetGravityNode,
    label: '设置重力',
    description: '设置物理世界的重力',
    icon: 'gravity',
    color: '#E91E63',
    tags: ['physics', 'gravity', 'world']
  });

  // 注册应用冲量节点
  registry.registerNodeType({
    type: 'physics/applyImpulse',
    category: NodeCategory.PHYSICS,
    constructor: ApplyImpulseNode,
    label: '应用冲量',
    description: '对物理体应用冲量',
    icon: 'impulse',
    color: '#E91E63',
    tags: ['physics', 'impulse', 'dynamics']
  });

  // 注册获取物理体属性节点
  registry.registerNodeType({
    type: 'physics/getPhysicsBodyProperties',
    category: NodeCategory.PHYSICS,
    constructor: GetPhysicsBodyPropertiesNode,
    label: '获取物理体属性',
    description: '获取物理体的各种属性',
    icon: 'properties',
    color: '#E91E63',
    tags: ['physics', 'properties', 'query']
  });

  // 注册碰撞事件监听节点
  registry.registerNodeType({
    type: 'physics/events/onCollision',
    category: NodeCategory.PHYSICS,
    constructor: OnCollisionEventNode,
    label: '碰撞事件',
    description: '监听物理体的碰撞事件',
    icon: 'collision',
    color: '#E91E63',
    tags: ['physics', 'collision', 'event']
  });

  // 注册设置物理体属性节点
  registry.registerNodeType({
    type: 'physics/setPhysicsBodyProperties',
    category: NodeCategory.PHYSICS,
    constructor: SetPhysicsBodyPropertiesNode,
    label: '设置物理体属性',
    description: '设置物理体的各种属性',
    icon: 'properties',
    color: '#E91E63',
    tags: ['physics', 'properties', 'set']
  });

  console.log('已注册所有物理节点类型');
}
