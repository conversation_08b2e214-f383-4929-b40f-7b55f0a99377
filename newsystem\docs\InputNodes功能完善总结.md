# InputNodes.ts 功能完善总结

## 概述

本次对 `engine/src/visualscript/presets/InputNodes.ts` 文件进行了全面的功能完善，将原本功能简单、只返回模拟数据的输入节点升级为一个功能完整、实际可用的输入系统。

## 原有问题分析

### 1. 架构问题
- **过时的基类**: 使用了不存在的 `VisualScriptNode` 基类
- **错误的API**: 使用了过时的 `addInput/addOutput` 方法
- **无实际功能**: 所有节点都只返回模拟数据

### 2. 功能缺失
- **无输入系统集成**: 没有连接到实际的输入事件
- **功能单一**: 缺少现代输入设备支持
- **无状态管理**: 缺少输入状态跟踪
- **无高级功能**: 缺少输入映射、序列检测等

## 主要完善内容

### 1. 新增输入管理系统

#### InputManager（输入管理器）
- **单例模式**: 全局统一的输入状态管理
- **多设备支持**: 键盘、鼠标、触摸、游戏手柄
- **事件驱动**: 基于浏览器事件系统
- **状态跟踪**: 完整的输入状态管理

#### 核心功能
```typescript
export class InputManager extends EventEmitter {
  // 状态管理
  private keyStates: Map<string, InputState>;
  private mouseStates: Map<number, InputState>;
  private touchPoints: Map<number, TouchPoint>;
  private gamepads: Map<number, Gamepad>;

  // 事件监听
  private setupKeyboardListeners();
  private setupMouseListeners();
  private setupTouchListeners();
  private setupGamepadListeners();
}
```

### 2. 新增枚举和接口

#### 输入设备类型
```typescript
enum InputDeviceType {
  KEYBOARD, MOUSE, TOUCH, GAMEPAD,
  ACCELEROMETER, GYROSCOPE
}
```

#### 输入事件类型
```typescript
enum InputEventType {
  KEY_DOWN, KEY_UP, MOUSE_DOWN, MOUSE_UP,
  TOUCH_START, TOUCH_END, GAMEPAD_CONNECTED
}
```

#### 输入状态接口
```typescript
interface InputState {
  pressed: boolean;
  justPressed: boolean;
  justReleased: boolean;
  pressTime: number;
  releaseTime: number;
}
```

### 3. 完全重写的输入节点

#### KeyboardInputNode（键盘输入节点）
**原有功能**：
- 只有基础的按键检测
- 返回模拟数据

**完善后功能**：
- 实际的按键状态检测
- 按键时间戳记录
- 按住持续时间计算
- 修饰键检查支持

**新增输出**：
- `pressTime`: 按下时间戳
- `holdDuration`: 按住持续时间

#### MouseInputNode（鼠标输入节点）
**原有功能**：
- 基础的鼠标按钮和位置
- 返回模拟数据

**完善后功能**：
- 实际的鼠标状态检测
- 多按钮支持（左、中、右、前进、后退）
- 鼠标移动增量计算
- 相对坐标支持

**新增输出**：
- `justPressed/justReleased`: 按钮状态变化
- `leftButton/rightButton/middleButton`: 各按钮独立状态

#### TouchInputNode（触摸输入节点）
**原有功能**：
- 基础的触摸检测
- 返回模拟数据

**完善后功能**：
- 多点触控支持
- 触摸点详细信息（压力、半径）
- 平均触摸位置计算
- 双指捏合检测

**新增输出**：
- `allTouches`: 所有触摸点信息
- `averagePosition`: 平均触摸位置
- `pinchDistance`: 双指间距离
- `isPinching`: 是否在捏合

#### GamepadInputNode（游戏手柄输入节点）
**原有功能**：
- 基础的手柄按钮和摇杆
- 返回模拟数据

**完善后功能**：
- 实际的游戏手柄API集成
- 摇杆死区处理
- 扳机值检测
- 方向键支持
- 连接状态检测

**新增输出**：
- `connected`: 连接状态
- `leftTrigger/rightTrigger`: 扳机值
- `dpad`: 方向键状态
- `buttons`: 所有按钮状态数组

### 4. 新增高级输入节点

#### InputMappingNode（输入映射节点）
- **功能**: 将多个按键映射到单一动作
- **用途**: 游戏中的动作绑定系统
- **特性**: 支持多键映射、触发键识别

```typescript
// 示例：跳跃动作可以绑定到空格键或W键
keyMappings: ['Space', 'KeyW']
actionName: 'jump'
```

#### InputSequenceNode（输入序列检测节点）
- **功能**: 检测按键序列输入
- **用途**: 格斗游戏连招、作弊码输入
- **特性**: 时间窗口控制、进度跟踪

```typescript
// 示例：检测 A-S-D 序列
sequence: ['KeyA', 'KeyS', 'KeyD']
timeWindow: 2000 // 2秒内完成
```

### 5. 技术特性

#### 跨平台兼容性
- **浏览器环境**: 使用标准Web API
- **事件处理**: 基于DOM事件系统
- **游戏手柄**: 使用Gamepad API
- **触摸**: 支持Touch Events API

#### 性能优化
- **事件驱动**: 只在输入变化时更新状态
- **状态缓存**: 避免重复计算
- **内存管理**: 自动清理过期数据
- **死区处理**: 摇杆输入的噪声过滤

#### 错误处理
- **设备检测**: 检查设备可用性
- **降级处理**: 设备不可用时的默认值
- **类型安全**: 完整的TypeScript类型定义

## 架构改进

### 1. 现代化的节点架构
```typescript
// 从过时的API
export class KeyboardInputNode extends VisualScriptNode {
  constructor() {
    super('KeyboardInput', '键盘输入');
    this.addInput('key', 'string', '按键');
  }
}

// 升级到现代API
export class KeyboardInputNode extends FunctionNode {
  protected initializeSockets(): void {
    this.addInput({
      name: 'key',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '按键代码'
    });
  }
}
```

### 2. 统一的输入管理
- 所有输入节点共享同一个InputManager实例
- 统一的事件处理和状态管理
- 一致的API设计和错误处理

### 3. 扩展性设计
- 易于添加新的输入设备类型
- 支持自定义输入映射
- 模块化的功能组件

## 使用示例

### 1. 基础键盘输入
```typescript
// 检测空格键
keyboardNode.setParameterValue('key', 'Space');
const result = keyboardNode.execute();
console.log(result.pressed); // 是否按下
console.log(result.holdDuration); // 按住时间
```

### 2. 鼠标拖拽检测
```typescript
// 检测左键拖拽
mouseNode.setParameterValue('button', 0);
const result = mouseNode.execute();
if (result.leftButton && result.delta.x !== 0) {
  console.log('正在拖拽');
}
```

### 3. 多点触控手势
```typescript
// 检测双指捏合
const result = touchNode.execute();
if (result.isPinching) {
  console.log('捏合距离:', result.pinchDistance);
}
```

### 4. 游戏手柄控制
```typescript
// 检测手柄连接和摇杆输入
gamepadNode.setParameterValue('deadzone', 0.1);
const result = gamepadNode.execute();
if (result.connected) {
  console.log('左摇杆:', result.leftStick);
}
```

### 5. 动作映射
```typescript
// 跳跃动作映射
mappingNode.setParameterValue('actionName', 'jump');
mappingNode.setParameterValue('keyMappings', ['Space', 'KeyW']);
const result = mappingNode.execute();
if (result.actionJustPressed) {
  console.log('跳跃动作触发');
}
```

## 节点统计

| 节点类型 | 原有数量 | 新增数量 | 总数量 |
|---------|---------|---------|--------|
| 基础输入节点 | 4 | 0 | 4 |
| 高级输入节点 | 0 | 2 | 2 |
| 管理类 | 0 | 1 | 1 |
| 总计 | 4 | 3 | 7 |

### 功能对比

| 功能 | 原有实现 | 完善后实现 |
|-----|---------|-----------|
| 键盘输入 | 模拟数据 | 实际按键检测 + 时间跟踪 |
| 鼠标输入 | 模拟数据 | 多按钮 + 位置 + 增量 |
| 触摸输入 | 模拟数据 | 多点触控 + 手势检测 |
| 手柄输入 | 模拟数据 | 完整手柄API + 死区处理 |
| 输入映射 | 无 | 多键映射到动作 |
| 序列检测 | 无 | 按键序列识别 |

## 兼容性说明

- 所有新功能都是向后兼容的
- 保持了原有的节点类型名称
- API升级但保持功能一致性
- 支持渐进式功能增强

这次完善将InputNodes.ts从一个功能缺失的模拟系统升级为一个功能完整、实际可用的现代输入系统，为视觉脚本提供了强大的输入处理能力。
