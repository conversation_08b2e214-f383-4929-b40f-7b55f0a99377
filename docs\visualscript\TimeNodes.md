# 时间节点文档

时间节点提供了丰富的时间相关功能，用于在可视化脚本中处理时间、延迟、计时、插值等操作。

## 节点列表

### 1. GetTimeNode (获取时间)

获取各种时间信息的节点。

**输出端口：**
- `time` (number): 当前时间(毫秒)
- `gameTime` (number): 游戏时间(秒)
- `realTime` (number): 真实时间(秒)
- `deltaTime` (number): 帧时间(秒)
- `unscaledDeltaTime` (number): 未缩放帧时间(秒)
- `timeScale` (number): 时间缩放
- `frameCount` (number): 帧数
- `fps` (number): FPS

**使用场景：**
- 获取当前游戏时间
- 监控帧率性能
- 时间相关的计算

### 2. DelayNode (延迟)

延迟执行的节点，支持进度监控和重置。

**输入端口：**
- `trigger` (exec): 触发延迟
- `duration` (number): 延迟时间(毫秒)
- `reset` (exec): 重置延迟

**输出端口：**
- `completed` (exec): 延迟完成
- `progress` (number): 进度(0-1)
- `remaining` (number): 剩余时间(毫秒)

**使用场景：**
- 延迟执行某个操作
- 创建时间间隔
- 实现倒计时功能

### 3. TimerNode (计时器)

高精度计时器，支持暂停、恢复和重置。

**输入端口：**
- `start` (exec): 开始计时
- `stop` (exec): 停止计时
- `pause` (exec): 暂停计时
- `resume` (exec): 恢复计时
- `reset` (exec): 重置计时器

**输出端口：**
- `elapsed` (number): 已用时间(毫秒)
- `elapsedSeconds` (number): 已用时间(秒)
- `isRunning` (boolean): 是否运行中
- `isPaused` (boolean): 是否暂停

**使用场景：**
- 测量代码执行时间
- 游戏计时功能
- 性能分析

### 4. IntervalNode (间隔执行)

按指定间隔重复执行的节点。

**输入端口：**
- `start` (exec): 开始间隔执行
- `stop` (exec): 停止间隔执行
- `interval` (number): 间隔时间(毫秒)
- `maxExecutions` (number): 最大执行次数(0=无限)

**输出端口：**
- `tick` (exec): 每次执行时触发
- `count` (number): 执行次数
- `isRunning` (boolean): 是否运行中

**使用场景：**
- 定期更新UI
- 周期性检查状态
- 重复动画效果

### 5. TimeCompareNode (时间比较)

比较两个时间值的节点。

**输入端口：**
- `timeA` (number): 时间A
- `timeB` (number): 时间B
- `tolerance` (number): 容差(毫秒)

**输出端口：**
- `isEqual` (boolean): 相等
- `isGreater` (boolean): A > B
- `isLess` (boolean): A < B
- `difference` (number): 差值(毫秒)

**使用场景：**
- 时间条件判断
- 同步检查
- 时间排序

### 6. TimeFormatNode (时间格式化)

将时间值格式化为可读字符串的节点。

**输入端口：**
- `time` (number): 时间(毫秒)
- `format` (string): 格式("HH:MM:SS", "MM:SS", "seconds")

**输出端口：**
- `formatted` (string): 格式化时间
- `hours` (number): 小时
- `minutes` (number): 分钟
- `seconds` (number): 秒
- `milliseconds` (number): 毫秒

**使用场景：**
- 显示游戏时间
- 倒计时显示
- 时间统计

### 7. TimeScaleNode (时间缩放)

控制游戏时间缩放的节点，支持平滑过渡。

**输入端口：**
- `trigger` (exec): 触发缩放
- `scale` (number): 缩放值
- `duration` (number): 过渡时间(毫秒)

**输出端口：**
- `completed` (exec): 缩放完成
- `currentScale` (number): 当前缩放

**使用场景：**
- 慢动作效果
- 快进功能
- 暂停游戏

### 8. TimeInterpolateNode (时间插值)

基于时间的数值插值节点，支持多种缓动函数。

**输入端口：**
- `from` (number): 起始值
- `to` (number): 目标值
- `duration` (number): 持续时间(毫秒)
- `easing` (string): 缓动类型
- `trigger` (exec): 触发插值
- `reset` (exec): 重置插值

**输出端口：**
- `value` (number): 当前值
- `progress` (number): 进度(0-1)
- `completed` (exec): 插值完成
- `isRunning` (boolean): 是否运行中

**支持的缓动类型：**
- `linear`: 线性
- `easein`: 缓入
- `easeout`: 缓出
- `easeinout`: 缓入缓出
- `bounce`: 弹跳

**使用场景：**
- 平滑动画
- 数值过渡
- UI动效

### 9. TimeSchedulerNode (时间调度器)

基于时间的事件调度节点。

**输入端口：**
- `start` (exec): 开始调度
- `stop` (exec): 停止调度
- `reset` (exec): 重置调度器
- `addEvent` (exec): 添加事件
- `eventTime` (number): 事件时间(毫秒)
- `eventData` (any): 事件数据

**输出端口：**
- `eventTriggered` (exec): 事件触发
- `eventData` (any): 事件数据
- `currentTime` (number): 当前时间
- `isRunning` (boolean): 是否运行中

**使用场景：**
- 复杂时间序列
- 事件调度
- 时间轴控制

## 使用示例

### 创建倒计时

```typescript
// 使用DelayNode创建10秒倒计时
const delayNode = new DelayNode();
const formatNode = new TimeFormatNode();

// 连接节点
delayNode.execute({ trigger: true, duration: 10000 });
const result = delayNode.execute({});
formatNode.execute({ time: result.remaining, format: 'MM:SS' });
```

### 实现慢动作效果

```typescript
// 使用TimeScaleNode实现慢动作
const timeScaleNode = new TimeScaleNode();

// 触发慢动作（0.5倍速，持续2秒）
timeScaleNode.execute({ 
  trigger: true, 
  scale: 0.5, 
  duration: 2000 
});
```

### 创建平滑动画

```typescript
// 使用TimeInterpolateNode创建平滑动画
const interpolateNode = new TimeInterpolateNode();

// 从0到100的缓入缓出动画，持续1秒
interpolateNode.execute({
  trigger: true,
  from: 0,
  to: 100,
  duration: 1000,
  easing: 'easeinout'
});
```

## 注意事项

1. **性能考虑**: 时间节点会频繁执行，注意避免在高频更新中进行复杂计算
2. **精度问题**: JavaScript的时间精度有限，对于高精度计时需求要特别注意
3. **内存管理**: 使用定时器的节点要确保正确清理，避免内存泄漏
4. **时间同步**: 在多人游戏中要注意时间同步问题
5. **浏览器兼容性**: performance.now()在某些旧浏览器中可能不可用

## 最佳实践

1. **合理使用缓存**: 对于不经常变化的时间值，可以缓存结果
2. **避免频繁创建**: 重复使用时间节点实例，避免频繁创建销毁
3. **错误处理**: 对时间相关的输入进行验证，确保数值合理
4. **调试支持**: 在开发阶段启用时间节点的调试输出
5. **文档记录**: 为复杂的时间逻辑添加详细注释
