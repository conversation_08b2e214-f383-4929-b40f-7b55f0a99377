/**
 * 视觉脚本数学节点
 * 提供数学运算相关的节点
 */
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, NodeType, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry, NodeTypeInfo } from '../nodes/NodeRegistry';

/**
 * 三角函数节点类型
 */
enum TrigonometricType {
  SIN = 'sin',
  COS = 'cos',
  TAN = 'tan',
  ASIN = 'asin',
  ACOS = 'acos',
  ATAN = 'atan',
  ATAN2 = 'atan2',
  SINH = 'sinh',
  COSH = 'cosh',
  TANH = 'tanh'
}

/**
 * 数学函数类型
 */
enum MathFunctionType {
  ABS = 'abs',
  SIGN = 'sign',
  FLOOR = 'floor',
  CEIL = 'ceil',
  ROUND = 'round',
  TRUNC = 'trunc',
  LOG = 'log',
  LOG10 = 'log10',
  LOG2 = 'log2',
  EXP = 'exp',
  EXP2 = 'exp2'
}

/**
 * 插值函数类型
 */
enum InterpolationType {
  LERP = 'lerp',
  SMOOTHSTEP = 'smoothstep',
  STEP = 'step'
}

/**
 * 向量组件
 */
interface Vector2 {
  x: number;
  y: number;
}

interface Vector3 {
  x: number;
  y: number;
  z: number;
}

interface Vector4 {
  x: number;
  y: number;
  z: number;
  w: number;
}

/**
 * 数学常数
 */
export const MathConstants = {
  PI: Math.PI,
  E: Math.E,
  LN2: Math.LN2,
  LN10: Math.LN10,
  LOG2E: Math.LOG2E,
  LOG10E: Math.LOG10E,
  SQRT1_2: Math.SQRT1_2,
  SQRT2: Math.SQRT2,
  TAU: Math.PI * 2,
  GOLDEN_RATIO: (1 + Math.sqrt(5)) / 2,
  DEG_TO_RAD: Math.PI / 180,
  RAD_TO_DEG: 180 / Math.PI
};

/**
 * 加法节点
 * 计算两个数的和
 */
export class AddNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加第一个数输入
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '第一个数',
      defaultValue: 0
    });

    // 添加第二个数输入
    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '第二个数',
      defaultValue: 0
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const a = this.getInputValue('a') as number;
    const b = this.getInputValue('b') as number;

    // 计算结果
    const result = a + b;

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 减法节点
 * 计算两个数的差
 */
export class SubtractNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加第一个数输入
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '第一个数',
      defaultValue: 0
    });

    // 添加第二个数输入
    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '第二个数',
      defaultValue: 0
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const a = this.getInputValue('a') as number;
    const b = this.getInputValue('b') as number;

    // 计算结果
    const result = a - b;

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 乘法节点
 * 计算两个数的积
 */
export class MultiplyNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加第一个数输入
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '第一个数',
      defaultValue: 0
    });

    // 添加第二个数输入
    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '第二个数',
      defaultValue: 0
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const a = this.getInputValue('a') as number;
    const b = this.getInputValue('b') as number;

    // 计算结果
    const result = a * b;

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 除法节点
 * 计算两个数的商
 */
export class DivideNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加第一个数输入
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '被除数',
      defaultValue: 0
    });

    // 添加第二个数输入
    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '除数',
      defaultValue: 1
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const a = this.getInputValue('a') as number;
    const b = this.getInputValue('b') as number;

    // 检查除数是否为0
    if (b === 0) {
      console.error('除数不能为0');
      this.triggerFlow('flow');
      return NaN;
    }

    // 计算结果
    const result = a / b;

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 取模节点
 * 计算两个数的模
 */
export class ModuloNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加第一个数输入
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '被除数',
      defaultValue: 0
    });

    // 添加第二个数输入
    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '除数',
      defaultValue: 1
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const a = this.getInputValue('a') as number;
    const b = this.getInputValue('b') as number;

    // 检查除数是否为0
    if (b === 0) {
      console.error('除数不能为0');
      this.triggerFlow('flow');
      return NaN;
    }

    // 计算结果
    const result = a % b;

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 幂运算节点
 * 计算一个数的幂
 */
export class PowerNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加底数输入
    this.addInput({
      name: 'base',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '底数',
      defaultValue: 0
    });

    // 添加指数输入
    this.addInput({
      name: 'exponent',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '指数',
      defaultValue: 1
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const base = this.getInputValue('base') as number;
    const exponent = this.getInputValue('exponent') as number;

    // 计算结果
    const result = Math.pow(base, exponent);

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 平方根节点
 * 计算一个数的平方根
 */
export class SquareRootNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加数值输入
    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '数值',
      defaultValue: 0
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const value = this.getInputValue('value') as number;

    // 检查输入值是否为负数
    if (value < 0) {
      console.error('不能计算负数的平方根');
      this.triggerFlow('flow');
      return NaN;
    }

    // 计算结果
    const result = Math.sqrt(value);

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 三角函数节点
 * 计算三角函数值
 */
export class TrigonometricNode extends FunctionNode {
  /** 三角函数类型 */
  private trigType: TrigonometricType;

  /**
   * 创建三角函数节点
   * @param options 节点选项
   */
  constructor(options: any) {
    super(options);

    // 设置三角函数类型
    this.trigType = options.metadata?.trigType || TrigonometricType.SIN;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加角度输入
    this.addInput({
      name: 'angle',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: this.trigType === TrigonometricType.ATAN2 ? 'Y值' : '角度（弧度）',
      defaultValue: 0
    });

    // 为atan2添加第二个参数
    if (this.trigType === TrigonometricType.ATAN2) {
      this.addInput({
        name: 'angle2',
        type: SocketType.DATA,
        direction: SocketDirection.INPUT,
        dataType: 'number',
        description: 'X值',
        defaultValue: 1
      });
    }

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const angle = this.getInputValue('angle') as number;
    const angle2 = this.getInputValue('angle2') as number; // 用于atan2

    // 根据三角函数类型计算结果
    let result: number;

    switch (this.trigType) {
      case TrigonometricType.SIN:
        result = Math.sin(angle);
        break;
      case TrigonometricType.COS:
        result = Math.cos(angle);
        break;
      case TrigonometricType.TAN:
        result = Math.tan(angle);
        break;
      case TrigonometricType.ASIN:
        result = Math.asin(angle);
        break;
      case TrigonometricType.ACOS:
        result = Math.acos(angle);
        break;
      case TrigonometricType.ATAN:
        result = Math.atan(angle);
        break;
      case TrigonometricType.ATAN2:
        result = Math.atan2(angle, angle2);
        break;
      case TrigonometricType.SINH:
        result = Math.sinh(angle);
        break;
      case TrigonometricType.COSH:
        result = Math.cosh(angle);
        break;
      case TrigonometricType.TANH:
        result = Math.tanh(angle);
        break;
      default:
        result = 0;
    }

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 数学函数节点
 * 提供各种数学函数
 */
export class MathFunctionNode extends FunctionNode {
  /** 数学函数类型 */
  private functionType: MathFunctionType;

  /**
   * 创建数学函数节点
   * @param options 节点选项
   */
  constructor(options: any) {
    super(options);

    // 设置数学函数类型
    this.functionType = options.metadata?.functionType || MathFunctionType.ABS;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加数值输入
    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '输入值',
      defaultValue: 0
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const value = this.getInputValue('value') as number;

    // 根据函数类型计算结果
    let result: number;

    switch (this.functionType) {
      case MathFunctionType.ABS:
        result = Math.abs(value);
        break;
      case MathFunctionType.SIGN:
        result = Math.sign(value);
        break;
      case MathFunctionType.FLOOR:
        result = Math.floor(value);
        break;
      case MathFunctionType.CEIL:
        result = Math.ceil(value);
        break;
      case MathFunctionType.ROUND:
        result = Math.round(value);
        break;
      case MathFunctionType.TRUNC:
        result = Math.trunc(value);
        break;
      case MathFunctionType.LOG:
        result = value > 0 ? Math.log(value) : NaN;
        break;
      case MathFunctionType.LOG10:
        result = value > 0 ? Math.log10(value) : NaN;
        break;
      case MathFunctionType.LOG2:
        result = value > 0 ? Math.log2(value) : NaN;
        break;
      case MathFunctionType.EXP:
        result = Math.exp(value);
        break;
      case MathFunctionType.EXP2:
        result = Math.pow(2, value);
        break;
      default:
        result = 0;
    }

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 最值函数节点
 * 计算最大值、最小值和限制值
 */
export class MinMaxNode extends FunctionNode {
  /** 操作类型 */
  private operation: 'min' | 'max' | 'clamp';

  /**
   * 创建最值函数节点
   * @param options 节点选项
   */
  constructor(options: any) {
    super(options);
    this.operation = options.metadata?.operation || 'min';
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加第一个值输入
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: this.operation === 'clamp' ? '要限制的值' : '第一个值',
      defaultValue: 0
    });

    // 添加第二个值输入
    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: this.operation === 'clamp' ? '最小值' : '第二个值',
      defaultValue: 0
    });

    // 为clamp操作添加第三个参数
    if (this.operation === 'clamp') {
      this.addInput({
        name: 'c',
        type: SocketType.DATA,
        direction: SocketDirection.INPUT,
        dataType: 'number',
        description: '最大值',
        defaultValue: 1
      });
    }

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const a = this.getInputValue('a') as number;
    const b = this.getInputValue('b') as number;
    const c = this.getInputValue('c') as number;

    let result: number;

    switch (this.operation) {
      case 'min':
        result = Math.min(a, b);
        break;
      case 'max':
        result = Math.max(a, b);
        break;
      case 'clamp':
        result = Math.min(Math.max(a, b), c);
        break;
      default:
        result = 0;
    }

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 随机数生成节点
 * 生成各种类型的随机数
 */
export class RandomNode extends FunctionNode {
  /** 随机数类型 */
  private randomType: 'random' | 'randomRange' | 'randomInt' | 'randomIntRange';

  /**
   * 创建随机数节点
   * @param options 节点选项
   */
  constructor(options: any) {
    super(options);
    this.randomType = options.metadata?.randomType || 'random';
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 根据随机数类型添加输入
    if (this.randomType === 'randomRange' || this.randomType === 'randomIntRange') {
      this.addInput({
        name: 'min',
        type: SocketType.DATA,
        direction: SocketDirection.INPUT,
        dataType: 'number',
        description: '最小值',
        defaultValue: 0
      });

      this.addInput({
        name: 'max',
        type: SocketType.DATA,
        direction: SocketDirection.INPUT,
        dataType: 'number',
        description: '最大值',
        defaultValue: 1
      });
    } else if (this.randomType === 'randomInt') {
      this.addInput({
        name: 'max',
        type: SocketType.DATA,
        direction: SocketDirection.INPUT,
        dataType: 'number',
        description: '最大值（不包含）',
        defaultValue: 10
      });
    }

    // 添加种子输入（可选）
    this.addInput({
      name: 'seed',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '随机种子（可选）',
      defaultValue: 0,
      optional: true
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '随机数结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const min = this.getInputValue('min') as number || 0;
    const max = this.getInputValue('max') as number || 1;
    const seed = this.getInputValue('seed') as number;

    let result: number;

    // 如果提供了种子且不为0，使用简单的伪随机数生成器
    const random = (seed && seed !== 0) ? this.seededRandom(seed) : Math.random();

    switch (this.randomType) {
      case 'random':
        result = random;
        break;
      case 'randomRange':
        result = min + random * (max - min);
        break;
      case 'randomInt':
        result = Math.floor(random * max);
        break;
      case 'randomIntRange':
        result = Math.floor(min + random * (max - min));
        break;
      default:
        result = random;
    }

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }

  /**
   * 基于种子的伪随机数生成器
   * @param seed 种子值
   * @returns 0-1之间的随机数
   */
  private seededRandom(seed: number): number {
    // 使用更好的伪随机数算法
    let x = Math.sin(seed) * 10000;
    x = x - Math.floor(x);

    // 确保结果在有效范围内
    if (!isFinite(x) || x < 0 || x >= 1) {
      console.warn('[随机数生成] 种子产生无效随机数，使用默认随机数');
      return Math.random();
    }

    return x;
  }
}

/**
 * 插值函数节点
 * 提供线性插值、平滑插值等功能
 */
export class InterpolationNode extends FunctionNode {
  /** 插值类型 */
  private interpolationType: InterpolationType;

  /**
   * 创建插值节点
   * @param options 节点选项
   */
  constructor(options: any) {
    super(options);
    this.interpolationType = options.metadata?.interpolationType || InterpolationType.LERP;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加起始值输入
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: this.interpolationType === InterpolationType.STEP ? '阈值' : '起始值',
      defaultValue: 0
    });

    // 添加结束值输入（除了step函数）
    if (this.interpolationType !== InterpolationType.STEP) {
      this.addInput({
        name: 'b',
        type: SocketType.DATA,
        direction: SocketDirection.INPUT,
        dataType: 'number',
        description: '结束值',
        defaultValue: 1
      });
    }

    // 添加插值参数输入
    this.addInput({
      name: 't',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: this.interpolationType === InterpolationType.STEP ? '输入值' : '插值参数（0-1）',
      defaultValue: 0.5
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '插值结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const a = this.getInputValue('a') as number;
    const b = this.getInputValue('b') as number;
    const t = this.getInputValue('t') as number;

    let result: number;

    switch (this.interpolationType) {
      case InterpolationType.LERP:
        result = this.lerp(a, b, t);
        break;
      case InterpolationType.SMOOTHSTEP:
        result = this.smoothstep(a, b, t);
        break;
      case InterpolationType.STEP:
        result = this.step(a, t);
        break;
      default:
        result = 0;
    }

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }

  /**
   * 线性插值
   * @param a 起始值
   * @param b 结束值
   * @param t 插值参数
   * @returns 插值结果
   */
  private lerp(a: number, b: number, t: number): number {
    return a + (b - a) * t;
  }

  /**
   * 平滑插值
   * @param a 起始值
   * @param b 结束值
   * @param t 插值参数
   * @returns 插值结果
   */
  private smoothstep(a: number, b: number, t: number): number {
    // 限制t在0-1范围内
    const clampedT = Math.max(0, Math.min(1, t));
    // 应用smoothstep公式
    const smoothT = clampedT * clampedT * (3 - 2 * clampedT);
    // 在a和b之间插值
    return a + (b - a) * smoothT;
  }

  /**
   * 阶跃函数
   * @param edge 阈值
   * @param x 输入值
   * @returns 阶跃结果
   */
  private step(edge: number, x: number): number {
    return x < edge ? 0 : 1;
  }
}

/**
 * 数值映射节点
 * 将数值从一个范围映射到另一个范围
 */
export class MapNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加输入值
    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '输入值',
      defaultValue: 0
    });

    // 添加输入范围
    this.addInput({
      name: 'inMin',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '输入最小值',
      defaultValue: 0
    });

    this.addInput({
      name: 'inMax',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '输入最大值',
      defaultValue: 1
    });

    // 添加输出范围
    this.addInput({
      name: 'outMin',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '输出最小值',
      defaultValue: 0
    });

    this.addInput({
      name: 'outMax',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '输出最大值',
      defaultValue: 1
    });

    // 添加是否限制输出范围
    this.addInput({
      name: 'clamp',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '是否限制输出范围',
      defaultValue: false,
      optional: true
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '映射结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const value = this.getInputValue('value') as number;
    const inMin = this.getInputValue('inMin') as number;
    const inMax = this.getInputValue('inMax') as number;
    const outMin = this.getInputValue('outMin') as number;
    const outMax = this.getInputValue('outMax') as number;
    const clamp = this.getInputValue('clamp') as boolean;

    // 检查输入范围是否有效
    const inputRange = inMax - inMin;
    if (inputRange === 0) {
      console.warn('[数值映射] 输入范围为0，返回输出最小值');
      return outMin;
    }

    // 计算映射结果
    let result = outMin + (value - inMin) * (outMax - outMin) / inputRange;

    // 如果需要限制范围
    if (clamp) {
      result = Math.min(Math.max(result, Math.min(outMin, outMax)), Math.max(outMin, outMax));
    }

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 向量运算节点
 * 提供2D和3D向量的基本运算
 */
export class VectorMathNode extends FunctionNode {
  /** 向量运算类型 */
  private operation: 'add' | 'subtract' | 'multiply' | 'divide' | 'dot' | 'cross' | 'length' | 'normalize' | 'distance';
  /** 向量维度 */
  private dimension: 2 | 3;

  /**
   * 创建向量运算节点
   * @param options 节点选项
   */
  constructor(options: any) {
    super(options);
    this.operation = options.metadata?.operation || 'add';
    this.dimension = options.metadata?.dimension || 3;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加第一个向量输入
    this.addInput({
      name: 'vectorA',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '向量A',
      defaultValue: this.dimension === 2 ? { x: 0, y: 0 } : { x: 0, y: 0, z: 0 }
    });

    // 对于需要两个向量的运算，添加第二个向量输入
    if (['add', 'subtract', 'multiply', 'divide', 'dot', 'cross', 'distance'].includes(this.operation)) {
      this.addInput({
        name: 'vectorB',
        type: SocketType.DATA,
        direction: SocketDirection.INPUT,
        dataType: 'object',
        description: '向量B',
        defaultValue: this.dimension === 2 ? { x: 1, y: 1 } : { x: 1, y: 1, z: 1 }
      });
    }

    // 添加标量输入（用于标量乘法等）
    if (['multiply', 'divide'].includes(this.operation)) {
      this.addInput({
        name: 'scalar',
        type: SocketType.DATA,
        direction: SocketDirection.INPUT,
        dataType: 'number',
        description: '标量值',
        defaultValue: 1,
        optional: true
      });
    }

    // 添加结果输出
    if (['dot', 'length', 'distance'].includes(this.operation)) {
      // 标量结果
      this.addOutput({
        name: 'result',
        type: SocketType.DATA,
        direction: SocketDirection.OUTPUT,
        dataType: 'number',
        description: '计算结果'
      });
    } else {
      // 向量结果
      this.addOutput({
        name: 'result',
        type: SocketType.DATA,
        direction: SocketDirection.OUTPUT,
        dataType: 'object',
        description: '结果向量'
      });
    }

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const vectorA = this.getInputValue('vectorA') as Vector2 | Vector3;
    const vectorB = this.getInputValue('vectorB') as Vector2 | Vector3;
    const scalar = this.getInputValue('scalar') as number || 1;

    let result: any;

    switch (this.operation) {
      case 'add':
        result = this.vectorAdd(vectorA, vectorB);
        break;
      case 'subtract':
        result = this.vectorSubtract(vectorA, vectorB);
        break;
      case 'multiply':
        result = scalar !== 1 ? this.vectorMultiplyScalar(vectorA, scalar) : this.vectorMultiply(vectorA, vectorB);
        break;
      case 'divide':
        result = scalar !== 1 ? this.vectorDivideScalar(vectorA, scalar) : this.vectorDivide(vectorA, vectorB);
        break;
      case 'dot':
        result = this.vectorDot(vectorA, vectorB);
        break;
      case 'cross':
        result = this.vectorCross(vectorA as Vector3, vectorB as Vector3);
        break;
      case 'length':
        result = this.vectorLength(vectorA);
        break;
      case 'normalize':
        result = this.vectorNormalize(vectorA);
        break;
      case 'distance':
        result = this.vectorDistance(vectorA, vectorB);
        break;
      default:
        result = vectorA;
    }

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }

  /**
   * 向量加法
   */
  private vectorAdd(a: Vector2 | Vector3, b: Vector2 | Vector3): Vector2 | Vector3 {
    if (this.dimension === 2) {
      return { x: a.x + b.x, y: a.y + b.y };
    } else {
      const a3 = a as Vector3;
      const b3 = b as Vector3;
      return { x: a3.x + b3.x, y: a3.y + b3.y, z: a3.z + b3.z };
    }
  }

  /**
   * 向量减法
   */
  private vectorSubtract(a: Vector2 | Vector3, b: Vector2 | Vector3): Vector2 | Vector3 {
    if (this.dimension === 2) {
      return { x: a.x - b.x, y: a.y - b.y };
    } else {
      const a3 = a as Vector3;
      const b3 = b as Vector3;
      return { x: a3.x - b3.x, y: a3.y - b3.y, z: a3.z - b3.z };
    }
  }

  /**
   * 向量乘法（逐分量）
   */
  private vectorMultiply(a: Vector2 | Vector3, b: Vector2 | Vector3): Vector2 | Vector3 {
    if (this.dimension === 2) {
      return { x: a.x * b.x, y: a.y * b.y };
    } else {
      const a3 = a as Vector3;
      const b3 = b as Vector3;
      return { x: a3.x * b3.x, y: a3.y * b3.y, z: a3.z * b3.z };
    }
  }

  /**
   * 向量标量乘法
   */
  private vectorMultiplyScalar(a: Vector2 | Vector3, scalar: number): Vector2 | Vector3 {
    if (this.dimension === 2) {
      return { x: a.x * scalar, y: a.y * scalar };
    } else {
      const a3 = a as Vector3;
      return { x: a3.x * scalar, y: a3.y * scalar, z: a3.z * scalar };
    }
  }

  /**
   * 向量除法（逐分量）
   */
  private vectorDivide(a: Vector2 | Vector3, b: Vector2 | Vector3): Vector2 | Vector3 {
    if (this.dimension === 2) {
      return { x: b.x !== 0 ? a.x / b.x : 0, y: b.y !== 0 ? a.y / b.y : 0 };
    } else {
      const a3 = a as Vector3;
      const b3 = b as Vector3;
      return {
        x: b3.x !== 0 ? a3.x / b3.x : 0,
        y: b3.y !== 0 ? a3.y / b3.y : 0,
        z: b3.z !== 0 ? a3.z / b3.z : 0
      };
    }
  }

  /**
   * 向量标量除法
   */
  private vectorDivideScalar(a: Vector2 | Vector3, scalar: number): Vector2 | Vector3 {
    if (scalar === 0 || !isFinite(scalar)) {
      console.warn('[向量标量除法] 标量为0或无穷大，返回原向量');
      return a;
    }

    if (this.dimension === 2) {
      return { x: a.x / scalar, y: a.y / scalar };
    } else {
      const a3 = a as Vector3;
      return { x: a3.x / scalar, y: a3.y / scalar, z: a3.z / scalar };
    }
  }

  /**
   * 向量点积
   */
  private vectorDot(a: Vector2 | Vector3, b: Vector2 | Vector3): number {
    if (this.dimension === 2) {
      return a.x * b.x + a.y * b.y;
    } else {
      const a3 = a as Vector3;
      const b3 = b as Vector3;
      return a3.x * b3.x + a3.y * b3.y + a3.z * b3.z;
    }
  }

  /**
   * 向量叉积（仅3D）
   */
  private vectorCross(a: Vector3, b: Vector3): Vector3 {
    return {
      x: a.y * b.z - a.z * b.y,
      y: a.z * b.x - a.x * b.z,
      z: a.x * b.y - a.y * b.x
    };
  }

  /**
   * 向量长度
   */
  private vectorLength(a: Vector2 | Vector3): number {
    if (this.dimension === 2) {
      return Math.sqrt(a.x * a.x + a.y * a.y);
    } else {
      const a3 = a as Vector3;
      return Math.sqrt(a3.x * a3.x + a3.y * a3.y + a3.z * a3.z);
    }
  }

  /**
   * 向量归一化
   */
  private vectorNormalize(a: Vector2 | Vector3): Vector2 | Vector3 {
    const length = this.vectorLength(a);
    if (length === 0 || !isFinite(length)) {
      console.warn('[向量归一化] 向量长度为0或无穷大，返回原向量');
      return a;
    }

    if (this.dimension === 2) {
      return { x: a.x / length, y: a.y / length };
    } else {
      const a3 = a as Vector3;
      return { x: a3.x / length, y: a3.y / length, z: a3.z / length };
    }
  }

  /**
   * 向量距离
   */
  private vectorDistance(a: Vector2 | Vector3, b: Vector2 | Vector3): number {
    const diff = this.vectorSubtract(a, b);
    return this.vectorLength(diff);
  }
}

/**
 * 数值验证节点
 * 检查数值的有效性
 */
export class NumberValidationNode extends FunctionNode {
  /** 验证类型 */
  private validationType: 'isNaN' | 'isFinite' | 'isInteger' | 'isPositive' | 'isNegative' | 'isZero';

  /**
   * 创建数值验证节点
   * @param options 节点选项
   */
  constructor(options: any) {
    super(options);
    this.validationType = options.metadata?.validationType || 'isNaN';
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加数值输入
    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '要验证的数值',
      defaultValue: 0
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '验证结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const value = this.getInputValue('value') as number;

    let result: boolean;

    switch (this.validationType) {
      case 'isNaN':
        result = isNaN(value);
        break;
      case 'isFinite':
        result = isFinite(value);
        break;
      case 'isInteger':
        result = Number.isInteger(value);
        break;
      case 'isPositive':
        result = value > 0;
        break;
      case 'isNegative':
        result = value < 0;
        break;
      case 'isZero':
        result = value === 0;
        break;
      default:
        result = false;
    }

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 数学常数节点
 * 提供常用的数学常数
 */
export class MathConstantNode extends FunctionNode {
  /** 常数类型 */
  private constantType: keyof typeof MathConstants;

  /**
   * 创建数学常数节点
   * @param options 节点选项
   */
  constructor(options: any) {
    super(options);
    this.constantType = options.metadata?.constantType || 'PI';
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加结果输出
    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '常数值'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取常数值
    const value = MathConstants[this.constantType];

    // 设置输出值
    this.setOutputValue('value', value);

    // 触发输出流程
    this.triggerFlow('flow');

    return value;
  }
}

/**
 * 注册数学节点
 * @param registry 节点注册表
 */
export function registerMathNodes(registry: NodeRegistry): void {
  // 注册加法节点
  registry.registerNodeType({
    type: 'math/basic/add',
    category: NodeCategory.MATH,
    constructor: AddNode,
    label: '加法',
    description: '计算两个数的和',
    icon: 'plus',
    color: '#2196F3',
    tags: ['math', 'basic', 'add']
  });

  // 注册减法节点
  registry.registerNodeType({
    type: 'math/basic/subtract',
    category: NodeCategory.MATH,
    constructor: SubtractNode,
    label: '减法',
    description: '计算两个数的差',
    icon: 'minus',
    color: '#2196F3',
    tags: ['math', 'basic', 'subtract']
  });

  // 注册乘法节点
  registry.registerNodeType({
    type: 'math/basic/multiply',
    category: NodeCategory.MATH,
    constructor: MultiplyNode,
    label: '乘法',
    description: '计算两个数的积',
    icon: 'multiply',
    color: '#2196F3',
    tags: ['math', 'basic', 'multiply']
  });

  // 注册除法节点
  registry.registerNodeType({
    type: 'math/basic/divide',
    category: NodeCategory.MATH,
    constructor: DivideNode,
    label: '除法',
    description: '计算两个数的商',
    icon: 'divide',
    color: '#2196F3',
    tags: ['math', 'basic', 'divide']
  });

  // 注册取模节点
  registry.registerNodeType({
    type: 'math/basic/modulo',
    category: NodeCategory.MATH,
    constructor: ModuloNode,
    label: '取模',
    description: '计算两个数的模',
    icon: 'modulo',
    color: '#2196F3',
    tags: ['math', 'basic', 'modulo']
  });

  // 注册幂运算节点
  registry.registerNodeType({
    type: 'math/advanced/power',
    category: NodeCategory.MATH,
    constructor: PowerNode,
    label: '幂运算',
    description: '计算一个数的幂',
    icon: 'power',
    color: '#2196F3',
    tags: ['math', 'advanced', 'power']
  });

  // 注册平方根节点
  registry.registerNodeType({
    type: 'math/advanced/sqrt',
    category: NodeCategory.MATH,
    constructor: SquareRootNode,
    label: '平方根',
    description: '计算一个数的平方根',
    icon: 'sqrt',
    color: '#2196F3',
    tags: ['math', 'advanced', 'sqrt']
  });

  // 注册正弦函数节点
  registry.registerNodeType({
    type: 'math/trigonometric/sin',
    category: NodeCategory.MATH,
    constructor: TrigonometricNode,
    label: '正弦',
    description: '计算正弦值',
    icon: 'sin',
    color: '#2196F3',
    tags: ['math', 'trigonometric', 'sin'],
    metadata: {
      trigType: TrigonometricType.SIN
    }
  });

  // 注册余弦函数节点
  registry.registerNodeType({
    type: 'math/trigonometric/cos',
    category: NodeCategory.MATH,
    constructor: TrigonometricNode,
    label: '余弦',
    description: '计算余弦值',
    icon: 'cos',
    color: '#2196F3',
    tags: ['math', 'trigonometric', 'cos'],
    metadata: {
      trigType: TrigonometricType.COS
    }
  });

  // 注册正切函数节点
  registry.registerNodeType({
    type: 'math/trigonometric/tan',
    category: NodeCategory.MATH,
    constructor: TrigonometricNode,
    label: '正切',
    description: '计算正切值',
    icon: 'tan',
    color: '#2196F3',
    tags: ['math', 'trigonometric', 'tan'],
    metadata: {
      trigType: TrigonometricType.TAN
    }
  });

  // 注册反正弦函数节点
  registry.registerNodeType({
    type: 'math/trigonometric/asin',
    category: NodeCategory.MATH,
    constructor: TrigonometricNode,
    label: '反正弦',
    description: '计算反正弦值',
    icon: 'asin',
    color: '#2196F3',
    tags: ['math', 'trigonometric', 'asin'],
    metadata: {
      trigType: TrigonometricType.ASIN
    }
  });

  // 注册反余弦函数节点
  registry.registerNodeType({
    type: 'math/trigonometric/acos',
    category: NodeCategory.MATH,
    constructor: TrigonometricNode,
    label: '反余弦',
    description: '计算反余弦值',
    icon: 'acos',
    color: '#2196F3',
    tags: ['math', 'trigonometric', 'acos'],
    metadata: {
      trigType: TrigonometricType.ACOS
    }
  });

  // 注册反正切函数节点
  registry.registerNodeType({
    type: 'math/trigonometric/atan',
    category: NodeCategory.MATH,
    constructor: TrigonometricNode,
    label: '反正切',
    description: '计算反正切值',
    icon: 'atan',
    color: '#2196F3',
    tags: ['math', 'trigonometric', 'atan'],
    metadata: {
      trigType: TrigonometricType.ATAN
    }
  });

  // 注册atan2函数节点
  registry.registerNodeType({
    type: 'math/trigonometric/atan2',
    category: NodeCategory.MATH,
    constructor: TrigonometricNode,
    label: 'Atan2',
    description: '计算两参数反正切值',
    icon: 'atan2',
    color: '#2196F3',
    tags: ['math', 'trigonometric', 'atan2'],
    metadata: {
      trigType: TrigonometricType.ATAN2
    }
  });

  // 注册双曲正弦函数节点
  registry.registerNodeType({
    type: 'math/trigonometric/sinh',
    category: NodeCategory.MATH,
    constructor: TrigonometricNode,
    label: '双曲正弦',
    description: '计算双曲正弦值',
    icon: 'sinh',
    color: '#2196F3',
    tags: ['math', 'trigonometric', 'sinh'],
    metadata: {
      trigType: TrigonometricType.SINH
    }
  });

  // 注册双曲余弦函数节点
  registry.registerNodeType({
    type: 'math/trigonometric/cosh',
    category: NodeCategory.MATH,
    constructor: TrigonometricNode,
    label: '双曲余弦',
    description: '计算双曲余弦值',
    icon: 'cosh',
    color: '#2196F3',
    tags: ['math', 'trigonometric', 'cosh'],
    metadata: {
      trigType: TrigonometricType.COSH
    }
  });

  // 注册双曲正切函数节点
  registry.registerNodeType({
    type: 'math/trigonometric/tanh',
    category: NodeCategory.MATH,
    constructor: TrigonometricNode,
    label: '双曲正切',
    description: '计算双曲正切值',
    icon: 'tanh',
    color: '#2196F3',
    tags: ['math', 'trigonometric', 'tanh'],
    metadata: {
      trigType: TrigonometricType.TANH
    }
  });

  // === 数学函数节点 ===

  // 注册绝对值节点
  registry.registerNodeType({
    type: 'math/function/abs',
    category: NodeCategory.MATH,
    constructor: MathFunctionNode,
    label: '绝对值',
    description: '计算数值的绝对值',
    icon: 'abs',
    color: '#4CAF50',
    tags: ['math', 'function', 'abs'],
    metadata: {
      functionType: MathFunctionType.ABS
    }
  });

  // 注册符号函数节点
  registry.registerNodeType({
    type: 'math/function/sign',
    category: NodeCategory.MATH,
    constructor: MathFunctionNode,
    label: '符号',
    description: '返回数值的符号',
    icon: 'sign',
    color: '#4CAF50',
    tags: ['math', 'function', 'sign'],
    metadata: {
      functionType: MathFunctionType.SIGN
    }
  });

  // 注册向下取整节点
  registry.registerNodeType({
    type: 'math/function/floor',
    category: NodeCategory.MATH,
    constructor: MathFunctionNode,
    label: '向下取整',
    description: '向下取整到最近的整数',
    icon: 'floor',
    color: '#4CAF50',
    tags: ['math', 'function', 'floor'],
    metadata: {
      functionType: MathFunctionType.FLOOR
    }
  });

  // 注册向上取整节点
  registry.registerNodeType({
    type: 'math/function/ceil',
    category: NodeCategory.MATH,
    constructor: MathFunctionNode,
    label: '向上取整',
    description: '向上取整到最近的整数',
    icon: 'ceil',
    color: '#4CAF50',
    tags: ['math', 'function', 'ceil'],
    metadata: {
      functionType: MathFunctionType.CEIL
    }
  });

  // 注册四舍五入节点
  registry.registerNodeType({
    type: 'math/function/round',
    category: NodeCategory.MATH,
    constructor: MathFunctionNode,
    label: '四舍五入',
    description: '四舍五入到最近的整数',
    icon: 'round',
    color: '#4CAF50',
    tags: ['math', 'function', 'round'],
    metadata: {
      functionType: MathFunctionType.ROUND
    }
  });

  // 注册截断节点
  registry.registerNodeType({
    type: 'math/function/trunc',
    category: NodeCategory.MATH,
    constructor: MathFunctionNode,
    label: '截断',
    description: '截断小数部分',
    icon: 'trunc',
    color: '#4CAF50',
    tags: ['math', 'function', 'trunc'],
    metadata: {
      functionType: MathFunctionType.TRUNC
    }
  });

  // 注册自然对数节点
  registry.registerNodeType({
    type: 'math/function/log',
    category: NodeCategory.MATH,
    constructor: MathFunctionNode,
    label: '自然对数',
    description: '计算自然对数',
    icon: 'log',
    color: '#4CAF50',
    tags: ['math', 'function', 'log'],
    metadata: {
      functionType: MathFunctionType.LOG
    }
  });

  // 注册常用对数节点
  registry.registerNodeType({
    type: 'math/function/log10',
    category: NodeCategory.MATH,
    constructor: MathFunctionNode,
    label: '常用对数',
    description: '计算以10为底的对数',
    icon: 'log10',
    color: '#4CAF50',
    tags: ['math', 'function', 'log10'],
    metadata: {
      functionType: MathFunctionType.LOG10
    }
  });

  // 注册二进制对数节点
  registry.registerNodeType({
    type: 'math/function/log2',
    category: NodeCategory.MATH,
    constructor: MathFunctionNode,
    label: '二进制对数',
    description: '计算以2为底的对数',
    icon: 'log2',
    color: '#4CAF50',
    tags: ['math', 'function', 'log2'],
    metadata: {
      functionType: MathFunctionType.LOG2
    }
  });

  // 注册指数函数节点
  registry.registerNodeType({
    type: 'math/function/exp',
    category: NodeCategory.MATH,
    constructor: MathFunctionNode,
    label: '指数函数',
    description: '计算e的x次幂',
    icon: 'exp',
    color: '#4CAF50',
    tags: ['math', 'function', 'exp'],
    metadata: {
      functionType: MathFunctionType.EXP
    }
  });

  // 注册2的幂函数节点
  registry.registerNodeType({
    type: 'math/function/exp2',
    category: NodeCategory.MATH,
    constructor: MathFunctionNode,
    label: '2的幂',
    description: '计算2的x次幂',
    icon: 'exp2',
    color: '#4CAF50',
    tags: ['math', 'function', 'exp2'],
    metadata: {
      functionType: MathFunctionType.EXP2
    }
  });

  // === 最值函数节点 ===

  // 注册最小值节点
  registry.registerNodeType({
    type: 'math/minmax/min',
    category: NodeCategory.MATH,
    constructor: MinMaxNode,
    label: '最小值',
    description: '返回两个数中的最小值',
    icon: 'min',
    color: '#FF9800',
    tags: ['math', 'minmax', 'min'],
    metadata: {
      operation: 'min'
    }
  });

  // 注册最大值节点
  registry.registerNodeType({
    type: 'math/minmax/max',
    category: NodeCategory.MATH,
    constructor: MinMaxNode,
    label: '最大值',
    description: '返回两个数中的最大值',
    icon: 'max',
    color: '#FF9800',
    tags: ['math', 'minmax', 'max'],
    metadata: {
      operation: 'max'
    }
  });

  // 注册限制值节点
  registry.registerNodeType({
    type: 'math/minmax/clamp',
    category: NodeCategory.MATH,
    constructor: MinMaxNode,
    label: '限制值',
    description: '将值限制在指定范围内',
    icon: 'clamp',
    color: '#FF9800',
    tags: ['math', 'minmax', 'clamp'],
    metadata: {
      operation: 'clamp'
    }
  });

  // === 随机数节点 ===

  // 注册随机数节点
  registry.registerNodeType({
    type: 'math/random/random',
    category: NodeCategory.MATH,
    constructor: RandomNode,
    label: '随机数',
    description: '生成0-1之间的随机数',
    icon: 'random',
    color: '#9C27B0',
    tags: ['math', 'random'],
    metadata: {
      randomType: 'random'
    }
  });

  // 注册范围随机数节点
  registry.registerNodeType({
    type: 'math/random/randomRange',
    category: NodeCategory.MATH,
    constructor: RandomNode,
    label: '范围随机数',
    description: '生成指定范围内的随机数',
    icon: 'randomRange',
    color: '#9C27B0',
    tags: ['math', 'random', 'range'],
    metadata: {
      randomType: 'randomRange'
    }
  });

  // 注册随机整数节点
  registry.registerNodeType({
    type: 'math/random/randomInt',
    category: NodeCategory.MATH,
    constructor: RandomNode,
    label: '随机整数',
    description: '生成0到指定值之间的随机整数',
    icon: 'randomInt',
    color: '#9C27B0',
    tags: ['math', 'random', 'integer'],
    metadata: {
      randomType: 'randomInt'
    }
  });

  // 注册范围随机整数节点
  registry.registerNodeType({
    type: 'math/random/randomIntRange',
    category: NodeCategory.MATH,
    constructor: RandomNode,
    label: '范围随机整数',
    description: '生成指定范围内的随机整数',
    icon: 'randomIntRange',
    color: '#9C27B0',
    tags: ['math', 'random', 'integer', 'range'],
    metadata: {
      randomType: 'randomIntRange'
    }
  });

  // === 插值函数节点 ===

  // 注册线性插值节点
  registry.registerNodeType({
    type: 'math/interpolation/lerp',
    category: NodeCategory.MATH,
    constructor: InterpolationNode,
    label: '线性插值',
    description: '在两个值之间进行线性插值',
    icon: 'lerp',
    color: '#607D8B',
    tags: ['math', 'interpolation', 'lerp'],
    metadata: {
      interpolationType: InterpolationType.LERP
    }
  });

  // 注册平滑插值节点
  registry.registerNodeType({
    type: 'math/interpolation/smoothstep',
    category: NodeCategory.MATH,
    constructor: InterpolationNode,
    label: '平滑插值',
    description: '在两个值之间进行平滑插值',
    icon: 'smoothstep',
    color: '#607D8B',
    tags: ['math', 'interpolation', 'smoothstep'],
    metadata: {
      interpolationType: InterpolationType.SMOOTHSTEP
    }
  });

  // 注册阶跃函数节点
  registry.registerNodeType({
    type: 'math/interpolation/step',
    category: NodeCategory.MATH,
    constructor: InterpolationNode,
    label: '阶跃函数',
    description: '阶跃函数，返回0或1',
    icon: 'step',
    color: '#607D8B',
    tags: ['math', 'interpolation', 'step'],
    metadata: {
      interpolationType: InterpolationType.STEP
    }
  });

  // === 数值映射节点 ===

  // 注册数值映射节点
  registry.registerNodeType({
    type: 'math/utility/map',
    category: NodeCategory.MATH,
    constructor: MapNode,
    label: '数值映射',
    description: '将数值从一个范围映射到另一个范围',
    icon: 'map',
    color: '#795548',
    tags: ['math', 'utility', 'map', 'range']
  });

  // === 向量运算节点 ===

  // 注册2D向量加法节点
  registry.registerNodeType({
    type: 'math/vector2/add',
    category: NodeCategory.MATH,
    constructor: VectorMathNode,
    label: '2D向量加法',
    description: '计算两个2D向量的和',
    icon: 'vectorAdd',
    color: '#E91E63',
    tags: ['math', 'vector', '2d', 'add'],
    metadata: {
      operation: 'add',
      dimension: 2
    }
  });

  // 注册2D向量减法节点
  registry.registerNodeType({
    type: 'math/vector2/subtract',
    category: NodeCategory.MATH,
    constructor: VectorMathNode,
    label: '2D向量减法',
    description: '计算两个2D向量的差',
    icon: 'vectorSubtract',
    color: '#E91E63',
    tags: ['math', 'vector', '2d', 'subtract'],
    metadata: {
      operation: 'subtract',
      dimension: 2
    }
  });

  // 注册2D向量长度节点
  registry.registerNodeType({
    type: 'math/vector2/length',
    category: NodeCategory.MATH,
    constructor: VectorMathNode,
    label: '2D向量长度',
    description: '计算2D向量的长度',
    icon: 'vectorLength',
    color: '#E91E63',
    tags: ['math', 'vector', '2d', 'length'],
    metadata: {
      operation: 'length',
      dimension: 2
    }
  });

  // 注册2D向量归一化节点
  registry.registerNodeType({
    type: 'math/vector2/normalize',
    category: NodeCategory.MATH,
    constructor: VectorMathNode,
    label: '2D向量归一化',
    description: '归一化2D向量',
    icon: 'vectorNormalize',
    color: '#E91E63',
    tags: ['math', 'vector', '2d', 'normalize'],
    metadata: {
      operation: 'normalize',
      dimension: 2
    }
  });

  // 注册3D向量加法节点
  registry.registerNodeType({
    type: 'math/vector3/add',
    category: NodeCategory.MATH,
    constructor: VectorMathNode,
    label: '3D向量加法',
    description: '计算两个3D向量的和',
    icon: 'vectorAdd',
    color: '#3F51B5',
    tags: ['math', 'vector', '3d', 'add'],
    metadata: {
      operation: 'add',
      dimension: 3
    }
  });

  // 注册3D向量减法节点
  registry.registerNodeType({
    type: 'math/vector3/subtract',
    category: NodeCategory.MATH,
    constructor: VectorMathNode,
    label: '3D向量减法',
    description: '计算两个3D向量的差',
    icon: 'vectorSubtract',
    color: '#3F51B5',
    tags: ['math', 'vector', '3d', 'subtract'],
    metadata: {
      operation: 'subtract',
      dimension: 3
    }
  });

  // 注册3D向量点积节点
  registry.registerNodeType({
    type: 'math/vector3/dot',
    category: NodeCategory.MATH,
    constructor: VectorMathNode,
    label: '3D向量点积',
    description: '计算两个3D向量的点积',
    icon: 'vectorDot',
    color: '#3F51B5',
    tags: ['math', 'vector', '3d', 'dot'],
    metadata: {
      operation: 'dot',
      dimension: 3
    }
  });

  // 注册3D向量叉积节点
  registry.registerNodeType({
    type: 'math/vector3/cross',
    category: NodeCategory.MATH,
    constructor: VectorMathNode,
    label: '3D向量叉积',
    description: '计算两个3D向量的叉积',
    icon: 'vectorCross',
    color: '#3F51B5',
    tags: ['math', 'vector', '3d', 'cross'],
    metadata: {
      operation: 'cross',
      dimension: 3
    }
  });

  // === 数值验证节点 ===

  // 注册NaN检查节点
  registry.registerNodeType({
    type: 'math/validation/isNaN',
    category: NodeCategory.MATH,
    constructor: NumberValidationNode,
    label: '检查NaN',
    description: '检查数值是否为NaN',
    icon: 'isNaN',
    color: '#FF5722',
    tags: ['math', 'validation', 'nan'],
    metadata: {
      validationType: 'isNaN'
    }
  });

  // 注册有限数检查节点
  registry.registerNodeType({
    type: 'math/validation/isFinite',
    category: NodeCategory.MATH,
    constructor: NumberValidationNode,
    label: '检查有限数',
    description: '检查数值是否为有限数',
    icon: 'isFinite',
    color: '#FF5722',
    tags: ['math', 'validation', 'finite'],
    metadata: {
      validationType: 'isFinite'
    }
  });

  // 注册整数检查节点
  registry.registerNodeType({
    type: 'math/validation/isInteger',
    category: NodeCategory.MATH,
    constructor: NumberValidationNode,
    label: '检查整数',
    description: '检查数值是否为整数',
    icon: 'isInteger',
    color: '#FF5722',
    tags: ['math', 'validation', 'integer'],
    metadata: {
      validationType: 'isInteger'
    }
  });

  // === 数学常数节点 ===

  // 注册PI常数节点
  registry.registerNodeType({
    type: 'math/constant/pi',
    category: NodeCategory.MATH,
    constructor: MathConstantNode,
    label: 'PI',
    description: '圆周率π',
    icon: 'pi',
    color: '#009688',
    tags: ['math', 'constant', 'pi'],
    metadata: {
      constantType: 'PI'
    }
  });

  // 注册E常数节点
  registry.registerNodeType({
    type: 'math/constant/e',
    category: NodeCategory.MATH,
    constructor: MathConstantNode,
    label: 'E',
    description: '自然常数e',
    icon: 'e',
    color: '#009688',
    tags: ['math', 'constant', 'e'],
    metadata: {
      constantType: 'E'
    }
  });

  // 注册度数转弧度常数节点
  registry.registerNodeType({
    type: 'math/constant/degToRad',
    category: NodeCategory.MATH,
    constructor: MathConstantNode,
    label: '度转弧度',
    description: '度数转弧度的转换常数',
    icon: 'degToRad',
    color: '#009688',
    tags: ['math', 'constant', 'conversion'],
    metadata: {
      constantType: 'DEG_TO_RAD'
    }
  });

  // 注册弧度转度数常数节点
  registry.registerNodeType({
    type: 'math/constant/radToDeg',
    category: NodeCategory.MATH,
    constructor: MathConstantNode,
    label: '弧度转度数',
    description: '弧度转度数的转换常数',
    icon: 'radToDeg',
    color: '#009688',
    tags: ['math', 'constant', 'conversion'],
    metadata: {
      constantType: 'RAD_TO_DEG'
    }
  });

  console.log('[数学节点] 已注册所有数学节点类型');
}
