/**
 * 视觉脚本网络协议节点
 * 提供不同网络协议的支持，如UDP、TCP、HTTP等
 */
import { AsyncNode } from '../nodes/AsyncNode';
import { EventNode, EventNodeOptions } from '../nodes/EventNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { NetworkSystem } from '../../network/NetworkSystem';

/**
 * UDP发送节点
 * 使用UDP协议发送数据
 */
export class UDPSendNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'address',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '目标地址',
      defaultValue: 'localhost'
    });

    this.addInput({
      name: 'port',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '目标端口',
      defaultValue: 8080
    });

    this.addInput({
      name: 'data',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '要发送的数据'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '发送成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '发送失败'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const address = this.getInputValue('address') as string;
    const port = this.getInputValue('port') as number;
    const data = this.getInputValue('data');

    // 检查输入值是否有效
    if (!address || !port || data === undefined) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取网络系统
    const networkSystem = this.context.world.getSystem(NetworkSystem);
    if (!networkSystem) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 使用网络系统发送消息（模拟UDP发送）
      // 在实际的UDP实现中，这里应该使用专门的UDP协议
      await networkSystem.sendToAll('udp_message', {
        address,
        port,
        data,
        timestamp: Date.now()
      });

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('UDP发送失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * HTTP请求节点
 * 发送HTTP请求
 */
export class HTTPRequestNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'url',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '请求URL',
      defaultValue: 'https://example.com'
    });

    this.addInput({
      name: 'method',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '请求方法',
      defaultValue: 'GET'
    });

    this.addInput({
      name: 'headers',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '请求头',
      defaultValue: {},
      optional: true
    });

    this.addInput({
      name: 'body',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '请求体',
      optional: true
    });

    this.addInput({
      name: 'timeout',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '超时时间（毫秒）',
      defaultValue: 5000,
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '请求成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '请求失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'response',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '响应数据'
    });

    this.addOutput({
      name: 'statusCode',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '状态码'
    });

    this.addOutput({
      name: 'headers',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '响应头'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const url = this.getInputValue('url') as string;
    const method = this.getInputValue('method') as string;
    const headers = this.getInputValue('headers') as Record<string, string>;
    const body = this.getInputValue('body');
    const timeout = this.getInputValue('timeout') as number;

    // 检查输入值是否有效
    if (!url) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取网络系统
    const networkSystem = this.context.world.getSystem(NetworkSystem);
    if (!networkSystem) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 使用fetch API发送HTTP请求
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const fetchOptions: RequestInit = {
        method,
        headers,
        signal: controller.signal
      };

      // 添加请求体
      if (body !== undefined) {
        fetchOptions.body = typeof body === 'string' ? body : JSON.stringify(body);
      }

      const response = await fetch(url, fetchOptions);
      clearTimeout(timeoutId);

      // 获取响应数据
      const responseData = await response.text();
      let parsedData: any;
      try {
        parsedData = JSON.parse(responseData);
      } catch {
        parsedData = responseData;
      }

      // 设置输出值
      this.setOutputValue('response', parsedData);
      this.setOutputValue('statusCode', response.status);
      this.setOutputValue('headers', Object.fromEntries(response.headers.entries()));

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('HTTP请求失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * WebSocket连接节点
 * 创建WebSocket连接
 */
export class WebSocketConnectNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'url',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: 'WebSocket URL',
      defaultValue: 'ws://localhost:8080'
    });

    this.addInput({
      name: 'protocols',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '协议列表',
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '连接成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '连接失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'WebSocket连接'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const url = this.getInputValue('url') as string;
    const protocols = this.getInputValue('protocols') as string[];

    // 检查输入值是否有效
    if (!url) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 创建WebSocket连接
      const ws = new WebSocket(url, protocols);

      // 等待连接建立
      await new Promise((resolve, reject) => {
        ws.onopen = () => resolve(ws);
        ws.onerror = (error) => reject(error);

        // 设置超时
        setTimeout(() => reject(new Error('连接超时')), 5000);
      });

      // 设置输出值
      this.setOutputValue('connection', ws);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('WebSocket连接失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * TCP连接节点
 * 创建TCP连接（在浏览器环境中模拟）
 */
export class TCPConnectNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'host',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '主机地址',
      defaultValue: 'localhost'
    });

    this.addInput({
      name: 'port',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '端口号',
      defaultValue: 8080
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '连接成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '连接失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'TCP连接'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const host = this.getInputValue('host') as string;
    const port = this.getInputValue('port') as number;

    // 检查输入值是否有效
    if (!host || !port) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 在浏览器环境中，TCP连接通常通过WebSocket或其他方式模拟
      // 这里创建一个模拟的TCP连接对象
      const connection = {
        host,
        port,
        connected: true,
        send: (data: any) => {
          console.log(`TCP发送到 ${host}:${port}:`, data);
        },
        close: () => {
          console.log(`TCP连接到 ${host}:${port} 已关闭`);
        }
      };

      // 设置输出值
      this.setOutputValue('connection', connection);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('TCP连接失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * WebSocket发送消息节点
 * 通过WebSocket连接发送消息
 */
export class WebSocketSendNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'WebSocket连接'
    });

    this.addInput({
      name: 'message',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '要发送的消息'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '发送成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '发送失败'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const connection = this.getInputValue('connection') as WebSocket;
    const message = this.getInputValue('message');

    // 检查输入值是否有效
    if (!connection || message === undefined) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 检查连接状态
      if (connection.readyState !== WebSocket.OPEN) {
        console.error('WebSocket连接未打开');
        this.triggerFlow('fail');
        return false;
      }

      // 发送消息
      const messageStr = typeof message === 'string' ? message : JSON.stringify(message);
      connection.send(messageStr);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('WebSocket发送消息失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * WebSocket接收消息事件节点
 * 监听WebSocket消息接收事件
 */
export class WebSocketReceiveNode extends EventNode {
  /** WebSocket连接 */
  private connection: WebSocket | null = null;

  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: EventNodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入数据插槽
    this.addInput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'WebSocket连接'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'message',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '收到消息'
    });

    this.addOutput({
      name: 'close',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '连接关闭'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '连接错误'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'data',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '消息数据'
    });

    this.addOutput({
      name: 'errorMessage',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  /**
   * 初始化事件
   */
  public initialize(): void {
    // 获取WebSocket连接
    this.connection = this.getInputValue('connection') as WebSocket;

    if (this.connection) {
      // 监听消息事件
      this.connection.onmessage = this.handleMessage.bind(this);
      this.connection.onclose = this.handleClose.bind(this);
      this.connection.onerror = this.handleError.bind(this);
    }
  }

  /**
   * 消息事件处理
   * @param event 消息事件
   */
  private handleMessage(event: MessageEvent): void {
    let data: any;
    try {
      // 尝试解析JSON
      data = JSON.parse(event.data);
    } catch {
      // 如果不是JSON，直接使用原始数据
      data = event.data;
    }

    // 设置输出值
    this.setOutputValue('data', data);

    // 触发输出流程
    this.triggerFlow('message');
  }

  /**
   * 连接关闭事件处理
   * @param event 关闭事件
   */
  private handleClose(event: CloseEvent): void {
    this.setOutputValue('errorMessage', `连接关闭: ${event.reason}`);
    this.triggerFlow('close');
  }

  /**
   * 错误事件处理
   * @param _event 错误事件
   */
  private handleError(_event: Event): void {
    this.setOutputValue('errorMessage', 'WebSocket连接错误');
    this.triggerFlow('error');
  }

  /**
   * 清理事件
   */
  public cleanup(): void {
    if (this.connection) {
      this.connection.onmessage = null;
      this.connection.onclose = null;
      this.connection.onerror = null;
      this.connection = null;
    }
  }
}

/**
 * REST API GET请求节点
 * 发送REST API GET请求
 */
export class RestApiGetNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'baseUrl',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: 'API基础URL',
      defaultValue: 'https://api.example.com'
    });

    this.addInput({
      name: 'endpoint',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: 'API端点',
      defaultValue: '/users'
    });

    this.addInput({
      name: 'queryParams',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '查询参数',
      defaultValue: {},
      optional: true
    });

    this.addInput({
      name: 'headers',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '请求头',
      defaultValue: { 'Content-Type': 'application/json' },
      optional: true
    });

    this.addInput({
      name: 'authToken',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '认证令牌',
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '请求成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '请求失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'data',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '响应数据'
    });

    this.addOutput({
      name: 'statusCode',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '状态码'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const baseUrl = this.getInputValue('baseUrl') as string;
    const endpoint = this.getInputValue('endpoint') as string;
    const queryParams = this.getInputValue('queryParams') as Record<string, any>;
    const headers = this.getInputValue('headers') as Record<string, string>;
    const authToken = this.getInputValue('authToken') as string;

    // 检查输入值是否有效
    if (!baseUrl || !endpoint) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 构建完整URL
      const url = new URL(endpoint, baseUrl);

      // 添加查询参数
      if (queryParams) {
        Object.entries(queryParams).forEach(([key, value]) => {
          url.searchParams.append(key, String(value));
        });
      }

      // 准备请求头
      const requestHeaders = { ...headers };
      if (authToken) {
        requestHeaders['Authorization'] = `Bearer ${authToken}`;
      }

      // 发送GET请求
      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: requestHeaders
      });

      // 解析响应
      const data = await response.json();

      // 设置输出值
      this.setOutputValue('data', data);
      this.setOutputValue('statusCode', response.status);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('REST API GET请求失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * REST API POST请求节点
 * 发送REST API POST请求
 */
export class RestApiPostNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'baseUrl',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: 'API基础URL',
      defaultValue: 'https://api.example.com'
    });

    this.addInput({
      name: 'endpoint',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: 'API端点',
      defaultValue: '/users'
    });

    this.addInput({
      name: 'body',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '请求体数据'
    });

    this.addInput({
      name: 'headers',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '请求头',
      defaultValue: { 'Content-Type': 'application/json' },
      optional: true
    });

    this.addInput({
      name: 'authToken',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '认证令牌',
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '请求成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '请求失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'data',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '响应数据'
    });

    this.addOutput({
      name: 'statusCode',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '状态码'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const baseUrl = this.getInputValue('baseUrl') as string;
    const endpoint = this.getInputValue('endpoint') as string;
    const body = this.getInputValue('body');
    const headers = this.getInputValue('headers') as Record<string, string>;
    const authToken = this.getInputValue('authToken') as string;

    // 检查输入值是否有效
    if (!baseUrl || !endpoint || !body) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 构建完整URL
      const url = new URL(endpoint, baseUrl);

      // 准备请求头
      const requestHeaders = { ...headers };
      if (authToken) {
        requestHeaders['Authorization'] = `Bearer ${authToken}`;
      }

      // 发送POST请求
      const response = await fetch(url.toString(), {
        method: 'POST',
        headers: requestHeaders,
        body: JSON.stringify(body)
      });

      // 解析响应
      const data = await response.json();

      // 设置输出值
      this.setOutputValue('data', data);
      this.setOutputValue('statusCode', response.status);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('REST API POST请求失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * GraphQL查询节点
 * 发送GraphQL查询请求
 */
export class GraphQLQueryNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'endpoint',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: 'GraphQL端点',
      defaultValue: 'https://api.example.com/graphql'
    });

    this.addInput({
      name: 'query',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: 'GraphQL查询语句'
    });

    this.addInput({
      name: 'variables',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '查询变量',
      defaultValue: {},
      optional: true
    });

    this.addInput({
      name: 'headers',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '请求头',
      defaultValue: { 'Content-Type': 'application/json' },
      optional: true
    });

    this.addInput({
      name: 'authToken',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '认证令牌',
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '查询成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '查询失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'data',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '查询结果'
    });

    this.addOutput({
      name: 'errors',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const endpoint = this.getInputValue('endpoint') as string;
    const query = this.getInputValue('query') as string;
    const variables = this.getInputValue('variables') as Record<string, any>;
    const headers = this.getInputValue('headers') as Record<string, string>;
    const authToken = this.getInputValue('authToken') as string;

    // 检查输入值是否有效
    if (!endpoint || !query) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 准备请求头
      const requestHeaders = { ...headers };
      if (authToken) {
        requestHeaders['Authorization'] = `Bearer ${authToken}`;
      }

      // 准备GraphQL请求体
      const requestBody = {
        query,
        variables: variables || {}
      };

      // 发送GraphQL请求
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: requestHeaders,
        body: JSON.stringify(requestBody)
      });

      // 解析响应
      const result = await response.json();

      // 设置输出值
      this.setOutputValue('data', result.data || {});
      this.setOutputValue('errors', result.errors || []);

      // 检查是否有错误
      if (result.errors && result.errors.length > 0) {
        console.error('GraphQL查询错误:', result.errors);
        this.triggerFlow('fail');
        return false;
      }

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('GraphQL查询失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * MQTT连接节点
 * 创建MQTT连接（通过WebSocket）
 */
export class MQTTConnectNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'brokerUrl',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: 'MQTT代理URL',
      defaultValue: 'ws://localhost:8083/mqtt'
    });

    this.addInput({
      name: 'clientId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '客户端ID',
      defaultValue: 'client_' + Math.random().toString(36).substring(7)
    });

    this.addInput({
      name: 'username',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '用户名',
      optional: true
    });

    this.addInput({
      name: 'password',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '密码',
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '连接成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '连接失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'client',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'MQTT客户端'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const brokerUrl = this.getInputValue('brokerUrl') as string;
    const clientId = this.getInputValue('clientId') as string;
    // 注意：username和password在实际MQTT实现中会被使用

    // 检查输入值是否有效
    if (!brokerUrl || !clientId) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 创建模拟的MQTT客户端（实际应用中需要使用MQTT.js库）
      const mqttClient = {
        brokerUrl,
        clientId,
        connected: false,
        connect: async () => {
          // 模拟连接过程
          await new Promise(resolve => setTimeout(resolve, 1000));
          mqttClient.connected = true;
          console.log(`MQTT客户端 ${clientId} 已连接到 ${brokerUrl}`);
        },
        disconnect: () => {
          mqttClient.connected = false;
          console.log(`MQTT客户端 ${clientId} 已断开连接`);
        },
        publish: (topic: string, message: string) => {
          if (mqttClient.connected) {
            console.log(`发布消息到主题 ${topic}:`, message);
          } else {
            console.error('MQTT客户端未连接');
          }
        },
        subscribe: (topic: string) => {
          if (mqttClient.connected) {
            console.log(`订阅主题: ${topic}`);
          } else {
            console.error('MQTT客户端未连接');
          }
        }
      };

      // 尝试连接
      await mqttClient.connect();

      // 设置输出值
      this.setOutputValue('client', mqttClient);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('MQTT连接失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 注册网络协议节点
 * @param registry 节点注册表
 */
export function registerNetworkProtocolNodes(registry: NodeRegistry): void {
  // 注册UDP发送节点
  registry.registerNodeType({
    type: 'network/protocol/udpSend',
    category: NodeCategory.NETWORK,
    constructor: UDPSendNode,
    label: 'UDP发送',
    description: '使用UDP协议发送数据',
    icon: 'udp',
    color: '#00BCD4',
    tags: ['network', 'protocol', 'udp', 'send']
  });

  // 注册HTTP请求节点
  registry.registerNodeType({
    type: 'network/protocol/httpRequest',
    category: NodeCategory.NETWORK,
    constructor: HTTPRequestNode,
    label: 'HTTP请求',
    description: '发送HTTP请求',
    icon: 'http',
    color: '#00BCD4',
    tags: ['network', 'protocol', 'http', 'request']
  });

  // 注册WebSocket连接节点
  registry.registerNodeType({
    type: 'network/protocol/websocketConnect',
    category: NodeCategory.NETWORK,
    constructor: WebSocketConnectNode,
    label: 'WebSocket连接',
    description: '创建WebSocket连接',
    icon: 'websocket',
    color: '#00BCD4',
    tags: ['network', 'protocol', 'websocket', 'connect']
  });

  // 注册WebSocket发送节点
  registry.registerNodeType({
    type: 'network/protocol/websocketSend',
    category: NodeCategory.NETWORK,
    constructor: WebSocketSendNode,
    label: 'WebSocket发送',
    description: '通过WebSocket连接发送消息',
    icon: 'send',
    color: '#00BCD4',
    tags: ['network', 'protocol', 'websocket', 'send']
  });

  // 注册WebSocket接收节点
  registry.registerNodeType({
    type: 'network/protocol/websocketReceive',
    category: NodeCategory.NETWORK,
    constructor: WebSocketReceiveNode,
    label: 'WebSocket接收',
    description: '监听WebSocket消息接收事件',
    icon: 'receive',
    color: '#00BCD4',
    tags: ['network', 'protocol', 'websocket', 'receive', 'event']
  });

  // 注册TCP连接节点
  registry.registerNodeType({
    type: 'network/protocol/tcpConnect',
    category: NodeCategory.NETWORK,
    constructor: TCPConnectNode,
    label: 'TCP连接',
    description: '创建TCP连接',
    icon: 'tcp',
    color: '#00BCD4',
    tags: ['network', 'protocol', 'tcp', 'connect']
  });

  // 注册REST API GET节点
  registry.registerNodeType({
    type: 'network/protocol/restApiGet',
    category: NodeCategory.NETWORK,
    constructor: RestApiGetNode,
    label: 'REST API GET',
    description: '发送REST API GET请求',
    icon: 'api',
    color: '#00BCD4',
    tags: ['network', 'protocol', 'rest', 'api', 'get']
  });

  // 注册REST API POST节点
  registry.registerNodeType({
    type: 'network/protocol/restApiPost',
    category: NodeCategory.NETWORK,
    constructor: RestApiPostNode,
    label: 'REST API POST',
    description: '发送REST API POST请求',
    icon: 'api',
    color: '#00BCD4',
    tags: ['network', 'protocol', 'rest', 'api', 'post']
  });

  // 注册GraphQL查询节点
  registry.registerNodeType({
    type: 'network/protocol/graphqlQuery',
    category: NodeCategory.NETWORK,
    constructor: GraphQLQueryNode,
    label: 'GraphQL查询',
    description: '发送GraphQL查询请求',
    icon: 'graphql',
    color: '#00BCD4',
    tags: ['network', 'protocol', 'graphql', 'query']
  });

  // 注册MQTT连接节点
  registry.registerNodeType({
    type: 'network/protocol/mqttConnect',
    category: NodeCategory.NETWORK,
    constructor: MQTTConnectNode,
    label: 'MQTT连接',
    description: '创建MQTT连接',
    icon: 'mqtt',
    color: '#00BCD4',
    tags: ['network', 'protocol', 'mqtt', 'connect']
  });
}
