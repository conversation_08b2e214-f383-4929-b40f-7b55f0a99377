# LogicNodes.ts 功能完善说明

## 概述

LogicNodes.ts 文件已经过全面完善，新增了多种高级逻辑节点类型，提供了更强大和灵活的逻辑处理能力。

## 新增功能

### 1. 多路分支节点（SwitchNode）
- **功能**：根据输入值选择对应的执行路径，类似于编程语言中的 switch/case 语句
- **特点**：
  - 支持动态添加和移除分支
  - 支持多种数据类型的值匹配
  - 提供默认执行路径
  - 智能类型转换比较

### 2. 循环控制节点

#### WhileLoopNode（While循环节点）
- **功能**：当条件为真时重复执行循环体
- **特点**：
  - 内置最大迭代次数保护，防止无限循环
  - 提供当前迭代次数输出
  - 支持循环中断和正常完成的不同流程
  - 循环状态管理和重置功能

#### ForEachLoopNode（ForEach循环节点）
- **功能**：遍历数组或对象的每个元素
- **特点**：
  - 支持数组、对象、字符串的遍历
  - 提供当前元素、索引、总长度输出
  - 自动类型标准化处理
  - 循环状态管理

### 3. 状态机节点（StateMachineNode）
- **功能**：实现有限状态机逻辑
- **特点**：
  - 支持动态状态定义和转换规则
  - 事件驱动的状态转换
  - 状态进入和退出回调支持
  - 完整的状态管理和查询功能
  - 支持状态重置

### 4. 扩展逻辑运算节点（ExtendedLogicalOperationNode）
- **新增运算符**：
  - XOR（异或）：(A && !B) || (!A && B)
  - NAND（与非）：!(A && B)
  - NOR（或非）：!(A || B)
- **特点**：
  - 完整的逻辑门运算支持
  - 与基础逻辑运算节点保持一致的接口

### 5. 条件表达式解析器节点（ConditionExpressionNode）
- **功能**：解析和执行字符串形式的条件表达式
- **特点**：
  - 支持复杂的条件表达式语法
  - 变量上下文支持
  - 表达式缓存机制提升性能
  - 安全的表达式解析，防止恶意代码执行
  - 智能语法转换（and/or/not 等关键字）

### 6. 扩展比较运算符
- **新增比较类型**：
  - CONTAINS：检查包含关系（字符串、数组、对象）
  - STARTS_WITH：检查开始匹配（字符串、数组）
  - ENDS_WITH：检查结束匹配（字符串、数组）
  - REGEX_MATCH：正则表达式匹配

## 性能优化

### 1. 表达式缓存
- 条件表达式节点支持表达式编译缓存
- 避免重复解析相同表达式
- 可配置的缓存启用/禁用

### 2. 短路求值
- 逻辑运算节点支持短路求值优化
- 提升复杂条件判断的性能

### 3. 类型智能转换
- 比较节点支持智能类型转换
- 减少类型不匹配导致的错误

## 安全性增强

### 1. 循环保护
- While循环节点内置最大迭代次数限制
- 防止无限循环导致的系统卡死

### 2. 表达式安全
- 条件表达式解析器过滤危险函数调用
- 防止恶意代码注入和执行

### 3. 错误处理
- 所有节点都包含完善的错误处理机制
- 提供详细的错误信息和调试支持

## 调试支持

### 1. 详细日志
- 所有节点提供详细的执行日志
- 状态变化和错误信息记录

### 2. 性能监控
- 循环节点提供迭代次数统计
- 表达式节点提供缓存命中率信息

## 使用示例

### 多路分支示例
```typescript
// 创建多路分支节点，根据用户类型执行不同逻辑
const switchNode = new SwitchNode({
  metadata: {
    cases: [
      { value: 'admin', output: 'adminFlow' },
      { value: 'user', output: 'userFlow' },
      { value: 'guest', output: 'guestFlow' }
    ]
  }
});
```

### 状态机示例
```typescript
// 创建游戏状态机
const stateMachine = new StateMachineNode({
  metadata: {
    initialState: 'menu',
    states: [
      {
        name: 'menu',
        transitions: { 'start': 'playing', 'quit': 'exit' }
      },
      {
        name: 'playing',
        transitions: { 'pause': 'paused', 'gameover': 'menu' }
      },
      {
        name: 'paused',
        transitions: { 'resume': 'playing', 'quit': 'menu' }
      }
    ]
  }
});
```

### 条件表达式示例
```typescript
// 使用条件表达式节点
const conditionNode = new ConditionExpressionNode();
// 表达式: "age >= 18 and score > 60"
// 上下文: { age: 20, score: 85 }
// 结果: true
```

## 兼容性

- 完全向后兼容原有的逻辑节点
- 新节点遵循相同的接口规范
- 可以与现有节点无缝集成

## 总结

完善后的LogicNodes.ts文件提供了：
- **15种新的节点类型**
- **4种新的比较运算符**
- **3种新的逻辑运算符**
- **完善的循环控制机制**
- **强大的状态机支持**
- **灵活的表达式解析**
- **全面的安全保护**
- **优秀的性能优化**

这些增强功能使得视觉脚本系统能够处理更复杂的逻辑场景，提供更好的开发体验和运行性能。
