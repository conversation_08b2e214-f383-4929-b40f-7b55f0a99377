# DebugNodes.ts 功能完善总结

## 概述

本次对 `engine/src/visualscript/presets/DebugNodes.ts` 文件进行了全面的功能完善，将原本功能简单的调试节点扩展为一个功能完整、智能化的调试和性能分析系统。

## 主要完善内容

### 1. 新增调试会话管理系统

#### DebugSessionManager（调试会话管理器）
- **单例模式**: 全局统一的调试会话管理
- **状态管理**: STOPPED、RUNNING、PAUSED、STEPPING
- **断点管理**: 添加、删除、检查断点
- **变量监视**: 监视变量变化
- **调用堆栈**: 跟踪节点调用链
- **事件系统**: 调试事件的发布和订阅

#### 新增枚举类型
- **DebugLevel**: 调试级别（TRACE、DEBUG、INFO、WARN、ERROR）
- **DebugSessionState**: 调试会话状态
- **DebugEventType**: 调试事件类型

### 2. 增强的断点节点（BreakpointNode）

#### 原有功能问题
- 断点功能不完整，只是简单的日志输出
- 缺少条件断点支持
- 没有命中次数统计
- 缺少与调试器的集成

#### 完善后的功能
```typescript
export class BreakpointNode extends FlowNode {
  private debugSession: DebugSessionManager;
  private hitCount: number = 0;

  // 新增功能：
  // 1. 条件断点支持
  // 2. 断点启用/禁用控制
  // 3. 命中次数统计和输出
  // 4. 与调试会话管理器集成
  // 5. 断点事件触发
  // 6. 调用堆栈管理
}
```

#### 新增输入插槽
- `enabled`: 是否启用断点
- `condition`: 断点条件（可选）
- `message`: 断点消息（可选）

#### 新增输出插槽
- `hitCount`: 断点命中次数

#### 新增方法
- `getBreakpointInfo()`: 获取断点信息
- `resetHitCount()`: 重置命中次数

### 3. 新增内存监控节点（MemoryMonitorNode）

#### 功能特性
- **实时内存监控**: 监控JavaScript堆内存使用
- **阈值检测**: 超过阈值时触发警告
- **内存变化跟踪**: 记录内存使用变化
- **跨平台支持**: 支持浏览器和Node.js环境

#### 输出数据
- `usedMemory`: 已使用内存（MB）
- `totalMemory`: 总内存（MB）
- `memoryUsagePercent`: 内存使用百分比
- `memoryDelta`: 内存变化量（MB）

#### 输出流程
- `flow`: 正常流程
- `thresholdExceeded`: 超过阈值时触发

### 4. 新增堆栈跟踪节点（StackTraceNode）

#### 功能特性
- **JavaScript堆栈跟踪**: 获取JavaScript调用堆栈
- **节点调用堆栈**: 集成视觉脚本节点调用链
- **堆栈合并**: 合并JavaScript和节点堆栈信息
- **可配置深度**: 支持设置最大堆栈深度

#### 输出数据
- `stackTrace`: 完整堆栈跟踪信息
- `callDepth`: 调用深度
- `currentFunction`: 当前函数名

#### 堆栈信息格式
```typescript
{
  functionName: string,
  source: string,
  type: 'javascript' | 'node',
  depth: number
}
```

### 5. 新增错误捕获节点（ErrorCatchNode）

#### 功能特性
- **Try-Catch-Finally模式**: 完整的错误处理流程
- **错误历史记录**: 保存错误历史，支持配置大小
- **错误分类**: 按错误类型分类处理
- **调试集成**: 与调试会话管理器集成

#### 流程控制
- `try`: 尝试执行的流程
- `success`: 成功执行后的流程
- `catch`: 捕获错误后的流程
- `finally`: 最终执行的流程

#### 错误信息输出
- `error`: 完整错误对象
- `errorMessage`: 错误消息
- `errorType`: 错误类型
- `errorHistory`: 错误历史记录

### 6. 新增性能分析节点（PerformanceProfilerNode）

#### 功能特性
- **异步性能分析**: 基于AsyncNode的长时间性能监控
- **多维度采样**: CPU、内存、执行时间等指标
- **可配置采样**: 支持设置采样间隔和持续时间
- **进度报告**: 实时报告分析进度
- **历史数据**: 保存多次分析结果

#### 分析指标
- **内存使用**: 平均、最大、最小内存使用
- **CPU使用率**: 平均、最大CPU使用率
- **执行时间**: 总执行时间和平均执行时间
- **采样数据**: 详细的采样点数据

#### 输出数据
```typescript
{
  profileName: string,
  duration: number,
  sampleCount: number,
  memory: {
    average: number,
    maximum: number,
    minimum: number,
    samples: number[]
  },
  cpu: {
    average: number,
    maximum: number,
    samples: number[]
  }
}
```

### 7. 增强的现有节点

#### LogNode增强
- 保持原有功能不变
- 与调试会话管理器集成
- 支持更多日志级别

#### PerformanceTimerNode增强
- 保持原有功能不变
- 改进了输入触发检测逻辑

#### VariableWatchNode增强
- 保持原有功能不变
- 改进了值比较和克隆逻辑

#### AssertNode增强
- 保持原有功能不变
- 与调试会话管理器集成

## 系统架构改进

### 1. 调试事件系统
```typescript
enum DebugEventType {
  BREAKPOINT_HIT = 'breakpoint_hit',
  STEP_COMPLETED = 'step_completed',
  EXECUTION_PAUSED = 'execution_paused',
  EXECUTION_RESUMED = 'execution_resumed',
  VARIABLE_CHANGED = 'variable_changed',
  ERROR_OCCURRED = 'error_occurred'
}
```

### 2. 统一的调试接口
- 所有调试节点都集成了DebugSessionManager
- 统一的事件触发和状态管理
- 一致的调试信息格式

### 3. 跨平台兼容性
- 浏览器环境：使用performance.memory
- Node.js环境：使用process.memoryUsage
- 降级处理：提供模拟数据

## 新增节点统计

| 节点类型 | 原有数量 | 新增数量 | 总数量 |
|---------|---------|---------|--------|
| 调试节点 | 5 | 4 | 9 |
| 管理类 | 0 | 1 | 1 |
| 总计 | 5 | 5 | 10 |

### 新增节点列表
1. **MemoryMonitorNode**: 内存监控节点
2. **StackTraceNode**: 堆栈跟踪节点
3. **ErrorCatchNode**: 错误捕获节点
4. **PerformanceProfilerNode**: 性能分析节点
5. **DebugSessionManager**: 调试会话管理器（单例）

## 使用示例

### 1. 断点调试
```typescript
// 设置条件断点
breakpointNode.setParameterValue('condition', 'variable > 100');
breakpointNode.setParameterValue('message', '变量超过阈值');
```

### 2. 内存监控
```typescript
// 监控内存使用，阈值50MB
memoryMonitorNode.setParameterValue('threshold', 50);
memoryMonitorNode.setParameterValue('logChanges', true);
```

### 3. 性能分析
```typescript
// 分析5秒钟的性能，每100ms采样一次
profilerNode.setParameterValue('duration', 5000);
profilerNode.setParameterValue('sampleInterval', 100);
```

### 4. 错误处理
```typescript
// 捕获错误并保存历史
errorCatchNode.setParameterValue('maxHistorySize', 20);
errorCatchNode.setParameterValue('logErrors', true);
```

## 性能优化

### 1. 内存管理
- 错误历史记录大小限制
- 性能分析数据的及时清理
- 堆栈跟踪深度限制

### 2. 异步处理
- 性能分析节点使用异步执行
- 支持取消长时间运行的分析
- 进度报告机制

### 3. 事件优化
- 使用EventEmitter进行事件管理
- 避免内存泄漏的监听器清理

## 兼容性说明

- 所有新增功能都是向后兼容的
- 现有的调试节点API保持不变
- 新增的功能通过可选参数提供
- 支持渐进式升级

这次完善将DebugNodes.ts从一个基础的调试工具集升级为一个功能完整、专业级的调试和性能分析系统，为视觉脚本的开发和调试提供了强大的支持。
