/**
 * 视觉脚本网络节点
 * 提供网络通信和多用户交互相关的节点
 */
import { FlowNode, FlowNodeOptions } from '../nodes/FlowNode';
import { EventNode, EventNodeOptions } from '../nodes/EventNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { NetworkSystem } from '../../network/NetworkSystem';
import { NetworkMessage } from '../../network/NetworkMessage';

// 导入其他网络节点模块
import { registerNetworkProtocolNodes } from './NetworkProtocolNodes';
import { registerWebRTCNodes } from './WebRTCNodes';
import { registerNetworkSecurityNodes } from './NetworkSecurityNodes';

/**
 * 网络连接节点
 * 连接到网络服务器
 */
export class ConnectToServerNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: FlowNodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'serverUrl',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '服务器URL',
      defaultValue: 'ws://localhost:8080'
    });

    this.addInput({
      name: 'roomId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '房间ID',
      defaultValue: '',
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '连接成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '连接失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'connected',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否已连接'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const serverUrl = this.getInputValue('serverUrl') as string;
    const roomId = this.getInputValue('roomId') as string;

    // 获取网络系统
    const networkSystem = this.context.world.getSystem(NetworkSystem);
    if (!networkSystem) {
      this.setOutputValue('connected', false);
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 连接到服务器
      networkSystem.connect(serverUrl, roomId || undefined);

      // 监听连接事件
      networkSystem.once('connected', () => {
        this.setOutputValue('connected', true);
        this.triggerFlow('success');
      });

      networkSystem.once('error', () => {
        this.setOutputValue('connected', false);
        this.triggerFlow('fail');
      });

      return true;
    } catch (error) {
      this.setOutputValue('connected', false);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 发送网络消息节点
 * 向其他用户发送网络消息
 */
export class SendNetworkMessageNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: FlowNodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'message',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '消息内容'
    });

    this.addInput({
      name: 'targetUserId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '目标用户ID（为空则发送给所有用户）',
      defaultValue: '',
      optional: true
    });

    this.addInput({
      name: 'reliable',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否可靠传输',
      defaultValue: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否发送成功'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const message = this.getInputValue('message');
    const targetUserId = this.getInputValue('targetUserId') as string;

    // 获取网络系统
    const networkSystem = this.context.world.getSystem(NetworkSystem);
    if (!networkSystem) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      // 发送消息
      if (targetUserId) {
        // 发送给特定用户
        await networkSystem.sendToUser(targetUserId, 'custom', message);
      } else {
        // 发送给所有用户
        await networkSystem.sendToAll('custom', message);
      }

      this.setOutputValue('success', true);
    } catch (error) {
      console.error('发送网络消息失败:', error);
      this.setOutputValue('success', false);
    }

    // 触发输出流程
    this.triggerFlow('flow');
    return true;
  }
}

/**
 * 网络消息接收事件节点
 * 当接收到网络消息时触发
 */
export class OnNetworkMessageNode extends EventNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: EventNodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'message',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '消息内容'
    });

    this.addOutput({
      name: 'senderId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '发送者ID'
    });
  }

  /**
   * 初始化事件
   */
  public initialize(): void {
    // 获取网络系统
    const networkSystem = this.context.world.getSystem(NetworkSystem);
    if (!networkSystem) {
      return;
    }

    // 监听网络消息事件
    networkSystem.on('message', this.onNetworkMessage.bind(this));
  }

  /**
   * 网络消息处理
   * @param message 网络消息
   * @param senderId 发送者ID
   */
  private onNetworkMessage(message: NetworkMessage, senderId: string): void {
    // 设置输出值
    this.setOutputValue('message', message.data);
    this.setOutputValue('senderId', senderId);

    // 触发输出流程
    this.triggerFlow('flow');
  }

  /**
   * 清理事件
   */
  public cleanup(): void {
    // 获取网络系统
    const networkSystem = this.context.world.getSystem(NetworkSystem);
    if (!networkSystem) {
      return;
    }

    // 移除事件监听
    networkSystem.off('message', this.onNetworkMessage.bind(this));
  }
}

/**
 * 断开网络连接节点
 * 断开与服务器的连接
 */
export class DisconnectFromServerNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: FlowNodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'disconnected',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否已断开连接'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取网络系统
    const networkSystem = this.context.world.getSystem(NetworkSystem);
    if (!networkSystem) {
      this.setOutputValue('disconnected', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      // 断开连接
      networkSystem.disconnect();
      this.setOutputValue('disconnected', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('断开网络连接失败:', error);
      this.setOutputValue('disconnected', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 获取网络状态节点
 * 获取当前网络连接状态
 */
export class GetNetworkStatusNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: FlowNodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'isConnected',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否已连接'
    });

    this.addOutput({
      name: 'connectionState',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '连接状态'
    });

    this.addOutput({
      name: 'userCount',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '在线用户数量'
    });

    this.addOutput({
      name: 'roomId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '当前房间ID'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取网络系统
    const networkSystem = this.context.world.getSystem(NetworkSystem);
    if (!networkSystem) {
      this.setOutputValue('isConnected', false);
      this.setOutputValue('connectionState', 'disconnected');
      this.setOutputValue('userCount', 0);
      this.setOutputValue('roomId', '');
      this.triggerFlow('flow');
      return false;
    }

    try {
      // 获取网络状态
      const state = networkSystem.getState();
      const isConnected = state === 'connected';
      const localUserId = networkSystem.getLocalUserId();

      this.setOutputValue('isConnected', isConnected);
      this.setOutputValue('connectionState', state);
      this.setOutputValue('userCount', 0); // 需要从网络管理器获取实际用户数量
      this.setOutputValue('roomId', localUserId || '');

      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('获取网络状态失败:', error);
      this.setOutputValue('isConnected', false);
      this.setOutputValue('connectionState', 'error');
      this.setOutputValue('userCount', 0);
      this.setOutputValue('roomId', '');
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 网络连接事件节点
 * 监听网络连接状态变化事件
 */
export class OnNetworkConnectionEventNode extends EventNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: EventNodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输出流程插槽
    this.addOutput({
      name: 'connected',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '连接成功'
    });

    this.addOutput({
      name: 'disconnected',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '连接断开'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '连接错误'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'connectionState',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '连接状态'
    });

    this.addOutput({
      name: 'errorMessage',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  /**
   * 初始化事件
   */
  public initialize(): void {
    // 获取网络系统
    const networkSystem = this.context.world.getSystem(NetworkSystem);
    if (!networkSystem) {
      return;
    }

    // 监听网络连接事件
    networkSystem.on('connected', this.onConnected.bind(this));
    networkSystem.on('disconnected', this.onDisconnected.bind(this));
    networkSystem.on('error', this.onError.bind(this));
  }

  /**
   * 连接成功处理
   */
  private onConnected(): void {
    this.setOutputValue('connectionState', 'connected');
    this.setOutputValue('errorMessage', '');
    this.triggerFlow('connected');
  }

  /**
   * 连接断开处理
   */
  private onDisconnected(): void {
    this.setOutputValue('connectionState', 'disconnected');
    this.setOutputValue('errorMessage', '');
    this.triggerFlow('disconnected');
  }

  /**
   * 连接错误处理
   * @param error 错误信息
   */
  private onError(error: any): void {
    this.setOutputValue('connectionState', 'error');
    this.setOutputValue('errorMessage', error?.message || '未知错误');
    this.triggerFlow('error');
  }

  /**
   * 清理事件
   */
  public cleanup(): void {
    // 获取网络系统
    const networkSystem = this.context.world.getSystem(NetworkSystem);
    if (!networkSystem) {
      return;
    }

    // 移除事件监听
    networkSystem.off('connected', this.onConnected.bind(this));
    networkSystem.off('disconnected', this.onDisconnected.bind(this));
    networkSystem.off('error', this.onError.bind(this));
  }
}

/**
 * 广播消息节点
 * 向所有连接的用户广播消息
 */
export class BroadcastMessageNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: FlowNodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'message',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '消息内容'
    });

    this.addInput({
      name: 'messageType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '消息类型',
      defaultValue: 'broadcast'
    });

    this.addInput({
      name: 'reliable',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否可靠传输',
      defaultValue: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否发送成功'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const message = this.getInputValue('message');
    const messageType = this.getInputValue('messageType') as string;

    // 获取网络系统
    const networkSystem = this.context.world.getSystem(NetworkSystem);
    if (!networkSystem) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      // 广播消息给所有用户
      await networkSystem.sendToAll(messageType, message);
      this.setOutputValue('success', true);
    } catch (error) {
      console.error('广播消息失败:', error);
      this.setOutputValue('success', false);
    }

    // 触发输出流程
    this.triggerFlow('flow');
    return true;
  }
}

/**
 * 注册网络节点
 * @param registry 节点注册表
 */
export function registerNetworkNodes(registry: NodeRegistry): void {
  // 注册连接到服务器节点
  registry.registerNodeType({
    type: 'network/connectToServer',
    category: NodeCategory.NETWORK,
    constructor: ConnectToServerNode,
    label: '连接到服务器',
    description: '连接到网络服务器',
    icon: 'connect',
    color: '#00BCD4',
    tags: ['network', 'connect', 'server']
  });

  // 注册断开连接节点
  registry.registerNodeType({
    type: 'network/disconnectFromServer',
    category: NodeCategory.NETWORK,
    constructor: DisconnectFromServerNode,
    label: '断开连接',
    description: '断开与服务器的连接',
    icon: 'disconnect',
    color: '#00BCD4',
    tags: ['network', 'disconnect', 'server']
  });

  // 注册获取网络状态节点
  registry.registerNodeType({
    type: 'network/getStatus',
    category: NodeCategory.NETWORK,
    constructor: GetNetworkStatusNode,
    label: '获取网络状态',
    description: '获取当前网络连接状态',
    icon: 'status',
    color: '#00BCD4',
    tags: ['network', 'status', 'info']
  });

  // 注册发送网络消息节点
  registry.registerNodeType({
    type: 'network/sendMessage',
    category: NodeCategory.NETWORK,
    constructor: SendNetworkMessageNode,
    label: '发送网络消息',
    description: '向其他用户发送网络消息',
    icon: 'send',
    color: '#00BCD4',
    tags: ['network', 'message', 'send']
  });

  // 注册广播消息节点
  registry.registerNodeType({
    type: 'network/broadcastMessage',
    category: NodeCategory.NETWORK,
    constructor: BroadcastMessageNode,
    label: '广播消息',
    description: '向所有连接的用户广播消息',
    icon: 'broadcast',
    color: '#00BCD4',
    tags: ['network', 'message', 'broadcast']
  });

  // 注册网络消息接收事件节点
  registry.registerNodeType({
    type: 'network/events/onMessage',
    category: NodeCategory.NETWORK,
    constructor: OnNetworkMessageNode,
    label: '接收网络消息',
    description: '当接收到网络消息时触发',
    icon: 'receive',
    color: '#00BCD4',
    tags: ['network', 'message', 'receive', 'event']
  });

  // 注册网络连接事件节点
  registry.registerNodeType({
    type: 'network/events/onConnection',
    category: NodeCategory.NETWORK,
    constructor: OnNetworkConnectionEventNode,
    label: '网络连接事件',
    description: '监听网络连接状态变化事件',
    icon: 'connection',
    color: '#00BCD4',
    tags: ['network', 'connection', 'event']
  });

  // 注册网络协议节点
  registerNetworkProtocolNodes(registry);

  // 注册WebRTC节点
  registerWebRTCNodes(registry);

  // 注册网络安全节点
  registerNetworkSecurityNodes(registry);

  console.log('已注册所有网络节点类型');
}
